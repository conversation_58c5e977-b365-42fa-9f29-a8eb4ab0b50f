# Material list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html
# EntityType list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/EntityType.html
# Attribute list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/attribute/Attribute.html
# Potion effects list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/potion/PotionEffectType.html
# Random UUID generator: https://www.uuidgenerator.net/
# Color codes: https://docs.adventure.kyori.net/minimessage.html#format
brunnera:
  itemname: <#34d8eb>Brunnera
  material: PAPER
  Pack:
    generate_model: true
    parent_model: block/cross
    textures:
    - default/flowers/brunnera.png
  Mechanics:
    stringblock:
      break_sound: block.grass.break
      place_sound: block.grass.place
      custom_variation: 1
      model: brunnera
      hardness: 2
      drop:
        silktouch: false
        loots:
        - oraxen_item: brunnera
          probability: 1.0

daffodil:
  itemname: <#f5ec42>Daffodil
  material: PAPER
  Pack:
    generate_model: true
    parent_model: block/cross
    textures:
    - default/flowers/daffodil.png
  Mechanics:
    stringblock:
      break_sound: block.grass.break
      place_sound: block.grass.place
      custom_variation: 2
      model: daffodil
      hardness: 2
      drop:
        silktouch: false
        loots:
        - oraxen_item: daffodil
          probability: 1.0

dailily:
  itemname: <#bf332c>Dailily
  material: PAPER
  Pack:
    generate_model: true
    parent_model: block/cross
    textures:
    - default/flowers/dailily.png
  Mechanics:
    stringblock:
      break_sound: block.grass.break
      place_sound: block.grass.place
      custom_variation: 3
      model: dailily
      hardness: 2
      drop:
        silktouch: false
        loots:
        - oraxen_item: dailily
          probability: 1.0
