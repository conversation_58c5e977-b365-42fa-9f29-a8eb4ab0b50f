#Wed Jul 23 22:56:08 EDT 2025
prefix=<blue>ServiceIO</blue> <dark_gray>»</dark_gray>
service.bank.none=<red>No bank service found</red>
service.character.none=<red>No character service found</red>
service.chat.none=<red>No chat service found</red>
service.convert.done=<gray><prefix> Completed conversion in <green><time></green> seconds, please verify the data before using it</gray>
service.convert.failed=<red><prefix> Conversion failed after <dark_red><time></dark_red> seconds, see the console for more information</red>
service.convert.running=<red><prefix> A conversion is already running</red>
service.convert.source-target=<red><prefix> Source and target service cannot be the same</red>
service.convert.start=<gray><prefix> Start converting data from <green><source></green> to <green><target></green>. This may take a while</gray>
service.economy.none=<red>No economy service found</red>
service.group.none=<red>No group service found</red>
service.hologram.none=<red>No hologram service found</red>
service.permission.none=<red>No permission service found</red>
service.provider.name=<\#0288D1><type>\: <provider></\#0288D1>
service.provider.registrations=<green><registered></green>
service.version=<gray>ServiceIO Information <dark_gray>(<green>v<version></green>)</dark_gray></gray>
