# #######################################################################################################################
#     ___       __       ___  __
#    / _ |__ __/ /____  / _ \/ /_ _____ _
#   / __ / // / __/ _ \/ ___/ / // / _ `/
#  /_/ |_\_,_/\__/\___/_/  /_/\_,_/\_, /
#                                 /___/ Plugins-Config
# Thank you for using AutoPlug!
# You can find detailed installation instructions here: https://autoplug.one/installer
# If there are any questions or you just want chat, join our Discord: https://discord.gg/GGNmtCC
# 
# #######################################################################################################################
# 
# 
# IMPORTANT: Before changing a setting in this file, make sure to read its explanation below! This will save you and me a lot of time in some cases.
# 
# 
# This file contains detailed information about your installed plugins. It is fetched from each plugins 'plugin.yml' file (located inside their jars).
# Example configuration for the EssentialsX plugin with each setting explained:
#   Essentials: 
#     exclude: false #### If a name/author/version is missing, the plugin gets excluded automatically
#      version: 1434 #### Gets fetched from 'plugin.yml' and refreshed after each check
#      latest-version: 1434 #### Gets refreshed after each check
#      author: Zenexer #### Gets fetched from 'plugin.yml' and refreshed after each check #### If multiple names are provided, only the first author will be used.
#     #### Note that only one id is necessary, provided both for demonstration purposes.
#     spigot-id: 871 #### Gets found by AutoPlugs' smart search algorithm and set in a check or can be set by you #### You can find it directly in the url. Example URLs id is 78414. Example URL: https://www.spigotmc.org/resources/autoplug-automatic-plugin-updater.78414/
#      bukkit-id: 93271 #### Gets found by AutoPlugs' smart search algorithm and set in a check or can be set by you #### Is the 'Project-ID' and can be found on the plugins bukkit site inside of the 'About' box at the right.
#      custom-check-url: #### Must link to a json file which contains a list/array of plugin versions where each item/object contains specific keys for version ("version_number", "version") and download URL ("download_url", "download", "file", "download_file").
#     custom-download-url: #### Must be a static url to the plugins latest jar file.
#     ignore-content-type: false #### When downloading a file the file host is asked for the file-type which must be .jar, when true this check is not performed.
#     force-update: false #### If true, downloads the update every time even if its already on the latest version. Do NOT enable, unless you have a really good reason to.
#     alternatives: #### below both alternatives are used for demonstration purposes, make sure to use only one)
#       github: 
#         repo-name: EssentialsX/Essentials #### Provided by you #### Can be found in its url: https://github.com/EssentialsX/Essentials
#         asset-name: EssentialsX #### Provided by you #### Wrong: 'EssentialsX-1.7.23.jar', we discard the version information.
#       jenkins: 
#         project-url: https://ci.ender.zone/job/EssentialsX/ #### Provided by you ### Note that each plugins jenkins url looks different.
#         artifact-name: EssentialsX #### Provided by you #### Wrong: 'EssentialsX-1.7.23.jar', we discard the version information.
#         build-id: 1434
#  #### The currently installed build identifier. Don't touch this.The configuration for uninstalled plugins wont be removed from this file, but they are automatically excluded from future checks (the exclude value is ignored).
# Note for plugin devs: You can add your spigot/bukkit-id to your plugin.yml file. For more information visit https://autoplug.one/faq/2
plugins: 
  general: 
    # Keep the plugins entry in this file even after its removal/uninstallation?
    keep-removed: true
  RoseResourcepack: 
    exclude: false
    version: 3.3.4
    latest-version: 3.3.4
    author: EmSockz
    spigot-id: 0
    modrinth-id: 7FAiLjDP
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  AutoPluginLoader: 
    exclude: false
    version: 1.5.1
    latest-version: 
    author: PCPSells
    spigot-id: 74225
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  ImageEmojis: 
    exclude: false
    version: 1.5.2
    latest-version: 1.5.2
    author: MrQuackDuck
    spigot-id: 0
    modrinth-id: cLBscQQD
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  FreedomChat: 
    exclude: false
    version: 1.7.5
    latest-version: 
    author: oharass
    spigot-id: 0
    modrinth-id: MubyTbnA
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  LuckPerms: 
    exclude: false
    version: 5.5.9
    latest-version: 
    author: Luck
    spigot-id: 28140
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  ServiceIO: 
    exclude: false
    version: 3.0.06
    latest-version: 3.0.06
    author: NonSwag
    spigot-id: 0
    modrinth-id: MNPyHOe7
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  ProtocolLib: 
    exclude: false
    version: 5.4.0753
    latest-version: 
    author: dmulloy2
    spigot-id: 1997
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  Oraxen: 
    exclude: true
    version: 1.191.0
    latest-version: 
    author: th0rgal
    spigot-id: 0
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  PlaceholderAPI: 
    exclude: false
    version: 2.11.7212
    latest-version: 
    author: HelpChat
    spigot-id: 6245
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  OpenJS: 
    exclude: false
    version: 1.1.0
    latest-version: 
    author: coolcostupit
    spigot-id: 0
    modrinth-id: zS3HK0Z8
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  CustomCrops: 
    exclude: false
    version: 3.6.42
    latest-version: 
    author: XiaoMoMi
    spigot-id: 0
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  eco: 
    exclude: false
    version: 6.76.2
    latest-version: 
    author: Auxilor
    spigot-id: 0
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  Actions: 
    exclude: false
    version: 2.74.1
    latest-version: 
    author: Auxilor
    spigot-id: 0
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  EcoArmor: 
    exclude: false
    version: 8.75.1
    latest-version: 
    author: Auxilor
    spigot-id: 88246
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  EcoEnchants: 
    exclude: false
    version: 12.23.1
    latest-version: 
    author: Auxilor
    spigot-id: 79573
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  EcoItems: 
    exclude: false
    version: 5.63.1
    latest-version: 
    author: Auxilor
    spigot-id: 117785
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
  EcoMobs: 
    exclude: false
    version: 10.21.1
    latest-version: 
    author: Auxilor
    spigot-id: 86576
    modrinth-id: 
    bukkit-id: 0
    ignore-content-type: false
    force-update: false
    custom-check-url: 
    custom-download-url: 
    alternatives: 
      github: 
        repo-name: 
        asset-name: 
      jenkins: 
        project-url: 
        artifact-name: 
        build-id: 0
