# #######################################################################################################################
#     ___       __       ___  __
#    / _ |__ __/ /____  / _ \/ /_ _____ _
#   / __ / // / __/ _ \/ ___/ / // / _ `/
#  /_/ |_\_,_/\__/\___/_/  /_/\_,_/\_, /
#                                 /___/ System-Config
# DO NOT CHANGE ANYTHING IN HERE, UNLESS YOU KNOW WHAT YOU ARE DOING!
# 
# #######################################################################################################################
config: 
  is-autostart-registered: false
  timestamp-last-updater-tasks: 23/07/2025 22:55:59
  timestamp-last-backup-task: 23/07/2025 22:55:35
  # If localhost is used below, remember to set this to false too!
  autoplug-web-ssl: true
  # Set to localhost to test on the local server.
  # Otherwise set to ************* or autoplug.one to connect with the default production server (remember to enable autoplug-web-ssl in this case too).
  autoplug-web-ip: *************
  autoplug-web-port: 35555
  autoplug-plugin-key: 1S20DxrPWaEidWkXer8jbgf7dpkvFxcvUxzqDcHXVGavVCkSjfaGUyG6eKjRoM3Kk289IqGU3LFhLc25GFVBQOWzRTkAcgb29fNSV0RjJmU7kJ327TAc8x0gWkbzdJiUKkSRixx7x2G4foy0BjyHTzdxvOxR0hifWVFcxUPpZugdQofpzyIdpf4O1xXRLWIb3Sim000Jmz9FMTqnKzZ0VN1yQJjPF8rabURXYI4pMkYtbnok3a1MlNKbYnkxDMPoORRK28skFrJ8XdZ5KJiqoexs8goh8kHpCQOXT4fMkRd3dlx8T5lTYqZ3ZdG5qk6YuX8MgMiHSEOuauS35LikJvVgACqbVVR9X1lbcTV4iaiA5gQgnpCvV0Acc8BHPSDNDKpVrIBFUteGESL7rzaruBY0kOEC9K9Z6UoMfH6UTUhYqNn12utmunGrThE3KdK706n8j6L7gZZkHUZ2IucNB7GJJhAFNUXQfqAL061UYCHntIJyJfYX
