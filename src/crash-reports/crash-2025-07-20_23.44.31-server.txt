---- Minecraft Crash Report ----
// But it works on my machine.
// 
// DO NOT REPORT THIS TO PAPER! REPORT TO PURPUR INSTEAD!
// 

Time: 2025-07-20 23:44:31
Description: Exception in server tick loop

java.lang.NoClassDefFoundError: Could not initialize class com.destroystokyo.paper.event.server.ServerExceptionEvent
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:72)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyAndAck(ServerPlayerGameMode.java:303)
	at net.minecraft.server.level.ServerPlayerGameMode.handleBlockBreakAction(ServerPlayerGameMode.java:189)
	at net.minecraft.server.network.ServerGamePacketListenerImpl.handlePlayerAction(ServerGamePacketListenerImpl.java:2044)
	at net.minecraft.network.protocol.game.ServerboundPlayerActionPacket.handle(ServerboundPlayerActionPacket.java:51)
	at net.minecraft.network.protocol.game.ServerboundPlayerActionPacket.handle(ServerboundPlayerActionPacket.java:10)
	at net.minecraft.network.protocol.PacketUtils.lambda$ensureRunningOnSameThread$0(PacketUtils.java:29)
	at net.minecraft.server.TickTask.run(TickTask.java:18)
	at net.minecraft.util.thread.BlockableEventLoop.doRunTask(BlockableEventLoop.java:155)
	at net.minecraft.util.thread.ReentrantBlockableEventLoop.doRunTask(ReentrantBlockableEventLoop.java:24)
	at net.minecraft.server.MinecraftServer.doRunTask(MinecraftServer.java:1485)
	at net.minecraft.server.MinecraftServer.doRunTask(MinecraftServer.java:176)
	at net.minecraft.util.thread.BlockableEventLoop.pollTask(BlockableEventLoop.java:129)
	at net.minecraft.server.MinecraftServer.pollTaskInternal(MinecraftServer.java:1465)
	at net.minecraft.server.MinecraftServer.pollTask(MinecraftServer.java:1459)
	at net.minecraft.util.thread.BlockableEventLoop.runAllTasks(BlockableEventLoop.java:118)
	at net.minecraft.server.MinecraftServer.waitUntilNextTick(MinecraftServer.java:1420)
	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1301)
	at net.minecraft.server.MinecraftServer.lambda$spin$2(MinecraftServer.java:313)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.ExceptionInInitializerError: Exception java.lang.StackOverflowError [in thread "Server thread"]
	at java.base/java.lang.StackStreamFactory$AbstractStackWalker.callStackWalk(Native Method)
	at java.base/java.lang.StackStreamFactory$AbstractStackWalker.beginStackWalk(StackStreamFactory.java:410)
	at java.base/java.lang.StackStreamFactory$AbstractStackWalker.walkHelper(StackStreamFactory.java:261)
	at java.base/java.lang.StackStreamFactory$AbstractStackWalker.walk(StackStreamFactory.java:253)
	at java.base/java.lang.StackWalker.walk(StackWalker.java:589)
	at org.bukkit.event.HandlerList.<init>(HandlerList.java:103)
	at com.destroystokyo.paper.event.server.ServerExceptionEvent.<clinit>(ServerExceptionEvent.java:16)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:72)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely(MineBlockEffect.kt:29)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:19)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.impl.EffectBreakBlock.onTrigger(EffectBreakBlock.kt:9)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Effect.trigger(Effect.kt:162)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ChainElement.doTrigger(ChainElement.kt:64)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger$trigger(ElementLike.kt:142)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:161)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.executors.impl.NormalExecutorFactory$NormalChainExecutor.execute(NormalExecutorFactory.kt:15)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger(Chain.kt:31)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.Chain.trigger$default(Chain.kt:27)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.doTrigger(EffectBlock.kt:89)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.ElementLike.trigger(ElementLike.kt:58)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.EffectBlock.tryTrigger(EffectBlock.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatchOnEffects(Trigger.kt:155)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch(Trigger.kt:81)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.Trigger.dispatch$default(Trigger.kt:77)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.triggers.impl.TriggerMineBlock.handle(TriggerMineBlock.kt:30)
	at co.aikar.timings.TimedEventExecutor.execute(TimedEventExecutor.java:80)
	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:71)
	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:54)
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:628)
	at net.minecraft.server.level.ServerPlayerGameMode.destroyBlock(ServerPlayerGameMode.java:333)
	at org.bukkit.craftbukkit.entity.CraftPlayer.breakBlock(CraftPlayer.java:1062)
	at libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.effects.templates.MineBlockEffect.breakBlocksSafely$lambda$1(MineBlockEffect.kt:36)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted$lambda$0(PlayerUtils.kt:25)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtils.runExempted(PlayerUtils.java:145)
	at eco-6.76.2-all.jar//com.willfp.eco.util.PlayerUtilsExtensions.runExempted(PlayerUtils.kt:25)


A detailed walkthrough of the error, its code path and all known details is as follows:
---------------------------------------------------------------------------------------

-- System Details --
Details:
	Minecraft Version: 1.21.7
	Minecraft Version ID: 1.21.7
	Operating System: Linux (amd64) version 6.15.6-zen1-1-zen
	Java Version: 21.0.7, Eclipse Adoptium
	Java VM Version: OpenJDK 64-Bit Server VM (mixed mode, sharing), Eclipse Adoptium
	Memory: 4884750184 bytes (4658 MiB) / 6442450944 bytes (6144 MiB) up to 6442450944 bytes (6144 MiB)
	CPUs: 12
	Processor Vendor: AuthenticAMD
	Processor Name: AMD Ryzen 5 5600X 6-Core Processor
	Identifier: AuthenticAMD Family 25 Model 33 Stepping 0
	Microarchitecture: Zen 3
	Frequency (GHz): -0.00
	Number of physical packages: 1
	Number of physical CPUs: 6
	Number of logical CPUs: 12
	Graphics card #0 name: GA104 [GeForce RTX 3060 Ti Lite Hash Rate]
	Graphics card #0 vendor: NVIDIA Corporation (0x10de)
	Graphics card #0 VRAM (MiB): 288.00
	Graphics card #0 deviceId: 0x2489
	Graphics card #0 versionInfo: unknown
	Virtual memory max (MiB): 47989.89
	Virtual memory used (MiB): 29414.72
	Swap memory total (MiB): 31993.00
	Swap memory used (MiB): 6770.63
	Space in storage for jna.tmpdir (MiB): <path not set>
	Space in storage for org.lwjgl.system.SharedLibraryExtractPath (MiB): <path not set>
	Space in storage for io.netty.native.workdir (MiB): <path not set>
	Space in storage for java.io.tmpdir (MiB): available: 15869.13, total: 15996.89
	Space in storage for workdir (MiB): available: 1239005.25, total: 1876685.25
	JVM Flags: 2 total; -Xms6144M -Xmx6144M
	CraftBukkit Information: 
   BrandInfo: Purpur (purpurmc:purpur) version 1.21.7-2477-HEAD@60bdf1c (2025-07-18T03:06:59Z)
   Running: Purpur version 1.21.7-2477-60bdf1c (MC: 1.21.7) (Implementing API version 1.21.7-R0.1-SNAPSHOT) true
   Plugins: { LuckPerms v5.5.9 me.lucko.luckperms.bukkit.loader.BukkitLoaderPlugin [Luck], ProtocolLib v5.4.0-SNAPSHOT-753 com.comphenix.protocol.ProtocolLib [dmulloy2, comphenix], PlaceholderAPI v2.11.7-DEV-212 me.clip.placeholderapi.PlaceholderAPIPlugin [HelpChat], ServiceIO v3.0.0-pre1 net.thenextlvl.service.ServicePlugin [NonSwag], eco v6.76.2 com.willfp.eco.internal.spigot.EcoImpl [Auxilor], EcoItems v5.63.1 com.willfp.ecoitems.EcoItemsPlugin [Auxilor], AutoPluginLoader v1.5.1 me.PCPSells.APLMain [[PCPSells]], EcoMobs v10.21.1 com.willfp.ecomobs.EcoMobsPlugin [Auxilor], OpenJS v1.1.0 coolcostupit.openjs.OpenJSPlugin [coolcostupit], EcoEnchants v12.23.1 com.willfp.ecoenchants.EcoEnchantsPlugin [Auxilor], EcoArmor v8.75.1 com.willfp.ecoarmor.EcoArmorPlugin [Auxilor], CustomCrops v3.6.42 net.momirealms.customcrops.bukkit.BukkitBootstrap [XiaoMoMi], FreedomChat v1.7.5 ru.bk.oharass.freedomchat.FreedomChat [oharass, sulu, ocelotpotpie, pop4959], RoseResourcepack v3.3.4 me.emsockz.roserp.RoseRP [EmSockz], Actions v2.74.1 com.willfp.actions.ActionsPlugin [Auxilor], ImageEmojis v1.5.2 mrquackduck.imageemojis.ImageEmojisPlugin [MrQuackDuck], libreforge v4.76.1 com.willfp.libreforge.LibreforgeSpigotPlugin [Auxilor],}
   Warnings: DEFAULT
   Reload Count: 0
   Threads: { TIMED_WAITING Worker-Main-3: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING Craft Scheduler Thread - 9: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Reference Handler: [java.base@21.0.7/java.lang.ref.Reference.waitForReferencePendingList(Native Method), java.base@21.0.7/java.lang.ref.Reference.processPendingReferences(Reference.java:246), java.base@21.0.7/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)], TIMED_WAITING Folia Async Scheduler Thread Timer: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING H2-serialization: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING HttpClient-2-Worker-0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING bStats-Metrics: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING bStats-Metrics: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING luckperms-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-4: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], RUNNABLE FileSystemWatchService: [java.base@21.0.7/sun.nio.fs.LinuxWatchService.poll(Native Method), java.base@21.0.7/sun.nio.fs.LinuxWatchService$Poller.run(LinuxWatchService.java:307), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Yggdrasil Key Fetcher: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE HttpClient-1-SelectorManager: [java.base@21.0.7/sun.nio.ch.EPoll.wait(Native Method), java.base@21.0.7/sun.nio.ch.EPollSelectorImpl.doSelect(EPollSelectorImpl.java:121), java.base@21.0.7/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.7/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142), platform/java.net.http@21.0.7/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1369)], TIMED_WAITING Craft Scheduler Thread - 10: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING pool-13-thread-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING luckperms-worker-5: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Libs-Watcher: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingDeque.takeFirst(LinkedBlockingDeque.java:485), java.base@21.0.7/java.util.concurrent.LinkedBlockingDeque.take(LinkedBlockingDeque.java:673), java.base@21.0.7/sun.nio.fs.AbstractWatchService.take(AbstractWatchService.java:118), openjs-1.1.0.jar//coolcostupit.openjs.modules.LibImporterApi.lambda$preLoad$0(LibImporterApi.java:45), openjs-1.1.0.jar//coolcostupit.openjs.modules.LibImporterApi$$Lambda/0x00007f83b9c3f1d8.run(Unknown Source), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING pool-20-thread-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Folia Async Scheduler Thread #1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:458), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.take(SynchronousQueue.java:318), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING bStats-Metrics: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING luckperms-worker-8: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING HttpClient-2-Worker-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE HttpClient-2-SelectorManager: [java.base@21.0.7/sun.nio.ch.EPoll.wait(Native Method), java.base@21.0.7/sun.nio.ch.EPollSelectorImpl.doSelect(EPollSelectorImpl.java:121), java.base@21.0.7/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130), java.base@21.0.7/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142), platform/java.net.http@21.0.7/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:1369)], TIMED_WAITING spark-monitoring-thread: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 8: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Signal Dispatcher: [], TIMED_WAITING Timer hack thread: [java.base@21.0.7/java.lang.Thread.sleep0(Native Method), java.base@21.0.7/java.lang.Thread.sleep(Thread.java:509), net.minecraft.Util$8.run(Util.java:923)], WAITING luckperms-worker-3: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING customcrops-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Watchdog Thread: [java.base@21.0.7/java.lang.Thread.sleep0(Native Method), java.base@21.0.7/java.lang.Thread.sleep(Thread.java:509), org.spigotmc.WatchdogThread.run(WatchdogThread.java:152)], RUNNABLE Server thread: [java.base@21.0.7/java.lang.Thread.dumpThreads(Native Method), java.base@21.0.7/java.lang.Thread.getAllStackTraces(Thread.java:2522), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:35), org.bukkit.craftbukkit.CraftCrashReport.get(CraftCrashReport.java:17), net.minecraft.SystemReport.setDetail(SystemReport.java:71), net.minecraft.CrashReport.<init>(CrashReport.java:39), net.minecraft.server.MinecraftServer.constructOrExtractCrashReport(MinecraftServer.java:1384), net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1318), net.minecraft.server.MinecraftServer.lambda$spin$2(MinecraftServer.java:313), net.minecraft.server.MinecraftServer$$Lambda/0x00007f83b8f0d000.run(Unknown Source), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING luckperms-worker-2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Folia Async Scheduler Thread #2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:458), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.take(SynchronousQueue.java:318), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING customcrops-world-worker-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Paper I/O Worker #0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], WAITING Paper Async Task Handler Thread - 0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING MVStore background writer /run/media/glitchy/Data/Projects/mc_server/src/plugins/LuckPerms/luckperms-h2-v2.mv.db: [java.base@21.0.7/java.lang.Object.wait0(Native Method), java.base@21.0.7/java.lang.Object.wait(Object.java:366), org.h2.mvstore.MVStore$BackgroundWriterThread.run(MVStore.java:3768)], WAITING customcrops-worker-0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING luckperms-worker-6: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Craft Async Scheduler Management Thread: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING ForkJoinPool.commonPool-worker-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING customcrops-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING customcrops-world-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING spark-async-sampler-worker-thread: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING customcrops-world-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Command Builder Thread Pool - 0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING customcrops-worker-3: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING Common-Cleaner: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.7/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.7/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), java.base@21.0.7/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583), java.base@21.0.7/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)], TIMED_WAITING Craft Scheduler Thread - 3: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING HttpClient-1-Worker-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Server console handler: [java.base@21.0.7/java.io.FileInputStream.readBytes(Native Method), java.base@21.0.7/java.io.FileInputStream.read(FileInputStream.java:287), java.base@21.0.7/java.io.BufferedInputStream.read1(BufferedInputStream.java:345), java.base@21.0.7/java.io.BufferedInputStream.implRead(BufferedInputStream.java:420), java.base@21.0.7/java.io.BufferedInputStream.read(BufferedInputStream.java:399), java.base@21.0.7/sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:350), java.base@21.0.7/sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:393), java.base@21.0.7/sun.nio.cs.StreamDecoder.lockedRead(StreamDecoder.java:217), java.base@21.0.7/sun.nio.cs.StreamDecoder.read(StreamDecoder.java:171), java.base@21.0.7/java.io.InputStreamReader.read(InputStreamReader.java:188), java.base@21.0.7/java.io.BufferedReader.fill(BufferedReader.java:160), java.base@21.0.7/java.io.BufferedReader.implReadLine(BufferedReader.java:370), java.base@21.0.7/java.io.BufferedReader.readLine(BufferedReader.java:347), java.base@21.0.7/java.io.BufferedReader.readLine(BufferedReader.java:436), net.minecrell.terminalconsole.SimpleTerminalConsole.readCommands(SimpleTerminalConsole.java:180), net.minecrell.terminalconsole.SimpleTerminalConsole.start(SimpleTerminalConsole.java:143), net.minecraft.server.dedicated.DedicatedServer$1.run(DedicatedServer.java:109)], TIMED_WAITING Craft Scheduler Thread - 7: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING ForkJoinPool-4-worker-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING pool-16-thread-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), java.base@21.0.7/java.util.concurrent.CompletableFuture$Signaller.block(CompletableFuture.java:1864), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.CompletableFuture.waitingGet(CompletableFuture.java:1898), java.base@21.0.7/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2072), platform/java.net.http@21.0.7/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:932), platform/java.net.http@21.0.7/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133), libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.configs.LibreforgeObjectConfig.share(LibreforgeObjectConfig.kt:57), libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.configs.LibreforgeObjectConfig.onRegister$lambda$3(LibreforgeObjectConfig.kt:86), libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.configs.LibreforgeObjectConfig$$Lambda/0x00007f83b92f57b8.invoke(Unknown Source), libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.configs.LibreforgeObjectConfigKt.onLrcdbThread$lambda$0(LibreforgeObjectConfig.kt:19), libreforge-4.76.1-1752792909195.jar//com.willfp.libreforge.configs.LibreforgeObjectConfigKt$$Lambda/0x00007f83b92f5bc8.call(Unknown Source), java.base@21.0.7/java.util.concurrent.FutureTask.run(FutureTask.java:317), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING HttpClient-1-Worker-0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING customcrops-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING ForkJoinPool.commonPool-worker-3: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING JNA Cleaner: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1852), java.base@21.0.7/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71), java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143), java.base@21.0.7/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218), com.sun.jna.internal.Cleaner$CleanerThread.run(Cleaner.java:154)], WAITING luckperms-worker-7: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING ForkJoinPool.commonPool-worker-4: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING customcrops-world-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING pool-9-thread-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING luckperms-worker-0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.compensatedBlock(ForkJoinPool.java:3740), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3723), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingDeque.takeFirst(LinkedBlockingDeque.java:485), java.base@21.0.7/java.util.concurrent.LinkedBlockingDeque.take(LinkedBlockingDeque.java:673), java.base@21.0.7/sun.nio.fs.AbstractWatchService.take(AbstractWatchService.java:118), me.lucko.luckperms.common.storage.implementation.file.watcher.AbstractFileWatcher.runEventProcessingLoop(AbstractFileWatcher.java:128), me.lucko.luckperms.common.storage.implementation.file.watcher.FileWatcher.lambda$new$0(FileWatcher.java:61), me.lucko.luckperms.common.storage.implementation.file.watcher.FileWatcher$$Lambda/0x00007f83b949b000.run(Unknown Source), java.base@21.0.7/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1423), java.base@21.0.7/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387), java.base@21.0.7/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312), java.base@21.0.7/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING placeholderapi-io-#0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Notification Thread: [], WAITING Folia Async Scheduler Thread #0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:458), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.take(SynchronousQueue.java:318), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE FileSystemWatchService: [java.base@21.0.7/sun.nio.fs.LinuxWatchService.poll(Native Method), java.base@21.0.7/sun.nio.fs.LinuxWatchService$Poller.run(LinuxWatchService.java:307), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 4: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING customcrops-scheduler: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Craft Scheduler Thread - 5 - RoseResourcepack: [java.base@21.0.7/sun.nio.ch.Net.accept(Native Method), java.base@21.0.7/sun.nio.ch.NioSocketImpl.accept(NioSocketImpl.java:748), java.base@21.0.7/java.net.ServerSocket.implAccept(ServerSocket.java:698), java.base@21.0.7/java.net.ServerSocket.platformImplAccept(ServerSocket.java:663), java.base@21.0.7/java.net.ServerSocket.implAccept(ServerSocket.java:639), java.base@21.0.7/java.net.ServerSocket.implAccept(ServerSocket.java:585), java.base@21.0.7/java.net.ServerSocket.accept(ServerSocket.java:543), RoseResourcepack-3.3.4.jar//me.emsockz.roserp.host.Hosting.lambda$handleRequest$0(Hosting.java:36), RoseResourcepack-3.3.4.jar//me.emsockz.roserp.host.Hosting$$Lambda/0x00007f83b9dbf4e8.run(Unknown Source), org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78), org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57), com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING customcrops-worker-2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING bStats-Metrics: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Common Worker #2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], WAITING ForkJoinPool.commonPool-worker-2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING bStats-Metrics: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING Craft Scheduler Thread - 6: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING luckperms-worker-4: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Paper Common Worker #1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], WAITING luckperms-worker-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Java2D Disposer: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:67), java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158), java.base@21.0.7/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:234), java.desktop@21.0.7/sun.java2d.Disposer.run(Disposer.java:145), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE DestroyJavaVM: [], TIMED_WAITING Craft Scheduler Thread - 0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Common Worker #0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.doRun(PrioritisedQueueExecutorThread.java:107), ca.spottedleaf.concurrentutil.executor.thread.PrioritisedQueueExecutorThread.run(PrioritisedQueueExecutorThread.java:49)], TIMED_WAITING customcrops-world-worker-0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING Timer-0: [java.base@21.0.7/java.lang.Object.wait0(Native Method), java.base@21.0.7/java.lang.Object.wait(Object.java:366), java.base@21.0.7/java.util.TimerThread.mainLoop(Timer.java:563), java.base@21.0.7/java.util.TimerThread.run(Timer.java:516)], WAITING H2-save: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Log4j2-AsyncAppenderEventDispatcher-1-Async: [java.base@21.0.7/java.io.FileOutputStream.writeBytes(Native Method), java.base@21.0.7/java.io.FileOutputStream.write(FileOutputStream.java:367), java.base@21.0.7/java.io.BufferedOutputStream.implWrite(BufferedOutputStream.java:217), java.base@21.0.7/java.io.BufferedOutputStream.write(BufferedOutputStream.java:200), java.base@21.0.7/java.io.PrintStream.implWrite(PrintStream.java:643), java.base@21.0.7/java.io.PrintStream.write(PrintStream.java:623), java.base@21.0.7/sun.nio.cs.StreamEncoder.writeBytes(StreamEncoder.java:309), java.base@21.0.7/sun.nio.cs.StreamEncoder.implWrite(StreamEncoder.java:381), java.base@21.0.7/sun.nio.cs.StreamEncoder.implWrite(StreamEncoder.java:357), java.base@21.0.7/sun.nio.cs.StreamEncoder.lockedWrite(StreamEncoder.java:158), java.base@21.0.7/sun.nio.cs.StreamEncoder.write(StreamEncoder.java:139), java.base@21.0.7/java.io.OutputStreamWriter.write(OutputStreamWriter.java:219), java.base@21.0.7/java.io.BufferedWriter.implFlushBuffer(BufferedWriter.java:178), java.base@21.0.7/java.io.BufferedWriter.flushBuffer(BufferedWriter.java:163), java.base@21.0.7/java.io.BufferedWriter.implWrite(BufferedWriter.java:334), java.base@21.0.7/java.io.BufferedWriter.write(BufferedWriter.java:313), java.base@21.0.7/java.io.Writer.write(Writer.java:278), java.base@21.0.7/java.io.PrintStream.implWrite(PrintStream.java:810), java.base@21.0.7/java.io.PrintStream.write(PrintStream.java:790), java.base@21.0.7/java.io.PrintStream.print(PrintStream.java:1002), net.minecrell.terminalconsole.TerminalConsoleAppender.print(TerminalConsoleAppender.java:269), net.minecrell.terminalconsole.TerminalConsoleAppender.append(TerminalConsoleAppender.java:256), org.apache.logging.log4j.core.config.AppenderControl.tryCallAppender(AppenderControl.java:160), org.apache.logging.log4j.core.config.AppenderControl.callAppender0(AppenderControl.java:133), org.apache.logging.log4j.core.config.AppenderControl.callAppenderPreventRecursion(AppenderControl.java:124), org.apache.logging.log4j.core.config.AppenderControl.callAppender(AppenderControl.java:88), org.apache.logging.log4j.core.appender.rewrite.RewriteAppender.append(RewriteAppender.java:89), org.apache.logging.log4j.core.config.AppenderControl.tryCallAppender(AppenderControl.java:160), org.apache.logging.log4j.core.config.AppenderControl.callAppender0(AppenderControl.java:133), org.apache.logging.log4j.core.config.AppenderControl.callAppenderPreventRecursion(AppenderControl.java:124), org.apache.logging.log4j.core.config.AppenderControl.callAppender(AppenderControl.java:88), org.apache.logging.log4j.core.appender.rewrite.RewriteAppender.append(RewriteAppender.java:89), org.apache.logging.log4j.core.config.AppenderControl.tryCallAppender(AppenderControl.java:160), org.apache.logging.log4j.core.config.AppenderControl.callAppender0(AppenderControl.java:133), org.apache.logging.log4j.core.config.AppenderControl.callAppenderPreventRecursion(AppenderControl.java:124), org.apache.logging.log4j.core.config.AppenderControl.callAppender(AppenderControl.java:88), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.dispatch(AsyncAppenderEventDispatcher.java:127), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.dispatchAll(AsyncAppenderEventDispatcher.java:91), org.apache.logging.log4j.core.appender.AsyncAppenderEventDispatcher.run(AsyncAppenderEventDispatcher.java:73)], WAITING Finalizer: [java.base@21.0.7/java.lang.Object.wait0(Native Method), java.base@21.0.7/java.lang.Object.wait(Object.java:366), java.base@21.0.7/java.lang.Object.wait(Object.java:339), java.base@21.0.7/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48), java.base@21.0.7/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158), java.base@21.0.7/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89), java.base@21.0.7/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)], RUNNABLE Netty Epoll Server IO #0: [io.netty.channel.epoll.Native.epollWait(Native Method), io.netty.channel.epoll.Native.epollWait(Native.java:220), io.netty.channel.epoll.Native.epollWait(Native.java:213), io.netty.channel.epoll.EpollEventLoop.epollWaitNoTimerChange(EpollEventLoop.java:308), io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:365), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Paper Async Command Builder Thread Pool - 1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], TIMED_WAITING customcrops-world-worker-2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkUntil(LockSupport.java:449), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1891), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING luckperms-worker-9: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], WAITING Paper Async Task Handler Thread - 1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519), java.base@21.0.7/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780), java.base@21.0.7/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1712), java.base@21.0.7/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], RUNNABLE Netty Epoll Server IO #1: [io.netty.channel.epoll.Native.epollWait0(Native Method), io.netty.channel.epoll.Native.epollWait(Native.java:193), io.netty.channel.epoll.EpollEventLoop.epollWait(EpollEventLoop.java:304), io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:368), io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998), io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING customcrops-worker-1: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING bStats-Metrics: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269), java.base@21.0.7/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182), java.base@21.0.7/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)], WAITING Worker-Main-2: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371), java.base@21.0.7/java.util.concurrent.ForkJoinPool.awaitWork(ForkJoinPool.java:1893), java.base@21.0.7/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1809), java.base@21.0.7/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)], TIMED_WAITING Keep-Alive-Timer: [java.base@21.0.7/java.lang.Thread.sleep0(Native Method), java.base@21.0.7/java.lang.Thread.sleep(Thread.java:509), java.base@21.0.7/sun.net.www.http.KeepAliveCache.run(KeepAliveCache.java:238), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583), java.base@21.0.7/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)], TIMED_WAITING process reaper: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583), java.base@21.0.7/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)], TIMED_WAITING User Authenticator #0: [java.base@21.0.7/jdk.internal.misc.Unsafe.park(Native Method), java.base@21.0.7/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:410), java.base@21.0.7/java.util.concurrent.LinkedTransferQueue$DualNode.await(LinkedTransferQueue.java:452), java.base@21.0.7/java.util.concurrent.SynchronousQueue$Transferer.xferLifo(SynchronousQueue.java:194), java.base@21.0.7/java.util.concurrent.SynchronousQueue.xfer(SynchronousQueue.java:235), java.base@21.0.7/java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:338), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130), java.base@21.0.7/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642), java.base@21.0.7/java.lang.Thread.runWith(Thread.java:1596), java.base@21.0.7/java.lang.Thread.run(Thread.java:1583)],}
   
   Force Loaded Chunks: { world: {}, world_nether: {}, world_the_end: {},}
	Server Running: true
	Player Count: 1 / 20; [ServerPlayer['Cunyarc'/4, uuid='66e9f0fe-0991-4fef-b0c2-5bd0354aa36f', l='ServerLevel[world]', x=-1077.02, y=79.00, z=-763.91, cpos=[-68, -48], tl=121, v=true](Cunyarc at -1077.022074452728,79.0,-763.9094299725062)]
	Active Data Packs: vanilla, file/bukkit, paper, file/oraxen_jukebox (incompatible)
	Available Data Packs: file/bukkit, file/oraxen_jukebox (incompatible), minecart_improvements, paper, redstone_experiments, trade_rebalance, vanilla
	Enabled Feature Flags: minecraft:vanilla
	World Generation: Experimental
	World Seed: 844717832202621481
	Suppressed Exceptions: ~~NONE~~
	Is Modded: Definitely; Server brand changed to 'Purpur'
	Type: Dedicated Server (map_server.txt)