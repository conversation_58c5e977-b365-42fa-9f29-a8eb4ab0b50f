[10-07-2025 13:51:32][AP][WARN] ================================
[10-07-2025 13:51:32][AP][WARN] Message: There was an api-error for RoseResourcepack!
[10-07-2025 13:51:32][AP][WARN] Details: Index 0 out of bounds for length 0
[10-07-2025 13:51:32][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[10-07-2025 13:51:32][AP][WARN] Stacktrace: 
[10-07-2025 13:51:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[10-07-2025 13:51:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[10-07-2025 13:51:32][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[10-07-2025 13:51:32][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[10-07-2025 13:51:32][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[10-07-2025 13:51:32][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[10-07-2025 13:51:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[10-07-2025 13:51:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[10-07-2025 13:51:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[10-07-2025 13:51:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[10-07-2025 13:51:32][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[10-07-2025 13:51:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[10-07-2025 13:51:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[10-07-2025 13:51:32][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[10-07-2025 13:51:32][AP][WARN] ================================
[11-07-2025 00:29:06][AP][WARN] ================================
[11-07-2025 00:29:06][AP][WARN] Message: There was an api-error for RoseResourcepack!
[11-07-2025 00:29:06][AP][WARN] Details: Index 0 out of bounds for length 0
[11-07-2025 00:29:06][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[11-07-2025 00:29:06][AP][WARN] Stacktrace: 
[11-07-2025 00:29:06][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[11-07-2025 00:29:06][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[11-07-2025 00:29:06][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[11-07-2025 00:29:06][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[11-07-2025 00:29:06][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[11-07-2025 00:29:06][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[11-07-2025 00:29:06][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[11-07-2025 00:29:06][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[11-07-2025 00:29:06][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[11-07-2025 00:29:06][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[11-07-2025 00:29:06][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[11-07-2025 00:29:06][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[11-07-2025 00:29:06][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[11-07-2025 00:29:06][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[11-07-2025 00:29:06][AP][WARN] ================================
[11-07-2025 01:47:40][AP][WARN] ================================
[11-07-2025 01:47:40][AP][WARN] Message: There was an api-error for RoseResourcepack!
[11-07-2025 01:47:40][AP][WARN] Details: Index 0 out of bounds for length 0
[11-07-2025 01:47:40][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[11-07-2025 01:47:40][AP][WARN] Stacktrace: 
[11-07-2025 01:47:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[11-07-2025 01:47:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[11-07-2025 01:47:40][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[11-07-2025 01:47:40][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[11-07-2025 01:47:40][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[11-07-2025 01:47:40][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[11-07-2025 01:47:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[11-07-2025 01:47:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[11-07-2025 01:47:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[11-07-2025 01:47:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[11-07-2025 01:47:40][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[11-07-2025 01:47:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[11-07-2025 01:47:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[11-07-2025 01:47:40][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[11-07-2025 01:47:40][AP][WARN] ================================
[13-07-2025 12:19:28][AP][WARN] ================================
[13-07-2025 12:19:28][AP][WARN] Message: There was an api-error for RoseResourcepack!
[13-07-2025 12:19:28][AP][WARN] Details: Index 0 out of bounds for length 0
[13-07-2025 12:19:28][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[13-07-2025 12:19:28][AP][WARN] Stacktrace: 
[13-07-2025 12:19:28][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[13-07-2025 12:19:28][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[13-07-2025 12:19:28][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[13-07-2025 12:19:28][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[13-07-2025 12:19:28][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[13-07-2025 12:19:28][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[13-07-2025 12:19:28][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[13-07-2025 12:19:28][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[13-07-2025 12:19:28][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[13-07-2025 12:19:28][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[13-07-2025 12:19:28][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[13-07-2025 12:19:28][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[13-07-2025 12:19:28][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[13-07-2025 12:19:28][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[13-07-2025 12:19:28][AP][WARN] ================================
[13-07-2025 14:15:42][AP][WARN] ================================
[13-07-2025 14:15:42][AP][WARN] Message: There was an api-error for RoseResourcepack!
[13-07-2025 14:15:42][AP][WARN] Details: Index 0 out of bounds for length 0
[13-07-2025 14:15:42][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[13-07-2025 14:15:42][AP][WARN] Stacktrace: 
[13-07-2025 14:15:42][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[13-07-2025 14:15:42][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[13-07-2025 14:15:42][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[13-07-2025 14:15:42][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[13-07-2025 14:15:42][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[13-07-2025 14:15:42][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[13-07-2025 14:15:42][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[13-07-2025 14:15:42][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[13-07-2025 14:15:42][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[13-07-2025 14:15:42][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[13-07-2025 14:15:42][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[13-07-2025 14:15:42][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[13-07-2025 14:15:42][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[13-07-2025 14:15:42][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[13-07-2025 14:15:42][AP][WARN] ================================
[13-07-2025 14:21:57][AP][WARN] ================================
[13-07-2025 14:21:57][AP][WARN] Message: There was an api-error for RoseResourcepack!
[13-07-2025 14:21:57][AP][WARN] Details: Index 0 out of bounds for length 0
[13-07-2025 14:21:57][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[13-07-2025 14:21:57][AP][WARN] Stacktrace: 
[13-07-2025 14:21:57][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[13-07-2025 14:21:57][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[13-07-2025 14:21:57][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[13-07-2025 14:21:57][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[13-07-2025 14:21:57][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[13-07-2025 14:21:57][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[13-07-2025 14:21:57][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[13-07-2025 14:21:57][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[13-07-2025 14:21:57][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[13-07-2025 14:21:57][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[13-07-2025 14:21:57][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[13-07-2025 14:21:57][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[13-07-2025 14:21:57][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[13-07-2025 14:21:57][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[13-07-2025 14:21:57][AP][WARN] ================================
[13-07-2025 14:22:32][AP][WARN] ================================
[13-07-2025 14:22:32][AP][WARN] Message: There was an api-error for RoseResourcepack!
[13-07-2025 14:22:32][AP][WARN] Details: Index 0 out of bounds for length 0
[13-07-2025 14:22:32][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[13-07-2025 14:22:32][AP][WARN] Stacktrace: 
[13-07-2025 14:22:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[13-07-2025 14:22:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[13-07-2025 14:22:32][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[13-07-2025 14:22:32][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[13-07-2025 14:22:32][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[13-07-2025 14:22:32][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[13-07-2025 14:22:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[13-07-2025 14:22:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[13-07-2025 14:22:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[13-07-2025 14:22:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[13-07-2025 14:22:32][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[13-07-2025 14:22:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[13-07-2025 14:22:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[13-07-2025 14:22:32][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[13-07-2025 14:22:32][AP][WARN] ================================
[13-07-2025 23:42:13][AP][WARN] ================================
[13-07-2025 23:42:13][AP][WARN] Message: There was an api-error for RoseResourcepack!
[13-07-2025 23:42:13][AP][WARN] Details: Index 0 out of bounds for length 0
[13-07-2025 23:42:13][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[13-07-2025 23:42:13][AP][WARN] Stacktrace: 
[13-07-2025 23:42:13][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[13-07-2025 23:42:13][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[13-07-2025 23:42:13][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[13-07-2025 23:42:13][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[13-07-2025 23:42:13][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[13-07-2025 23:42:13][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[13-07-2025 23:42:13][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[13-07-2025 23:42:13][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[13-07-2025 23:42:13][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[13-07-2025 23:42:13][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[13-07-2025 23:42:13][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[13-07-2025 23:42:13][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[13-07-2025 23:42:13][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[13-07-2025 23:42:13][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[13-07-2025 23:42:13][AP][WARN] ================================
[14-07-2025 12:41:04][AP][WARN] ================================
[14-07-2025 12:41:04][AP][WARN] Message: There was an api-error for RoseResourcepack!
[14-07-2025 12:41:04][AP][WARN] Details: Index 0 out of bounds for length 0
[14-07-2025 12:41:04][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[14-07-2025 12:41:04][AP][WARN] Stacktrace: 
[14-07-2025 12:41:04][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[14-07-2025 12:41:04][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[14-07-2025 12:41:04][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[14-07-2025 12:41:04][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[14-07-2025 12:41:04][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[14-07-2025 12:41:04][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[14-07-2025 12:41:04][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[14-07-2025 12:41:04][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[14-07-2025 12:41:04][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[14-07-2025 12:41:04][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[14-07-2025 12:41:04][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[14-07-2025 12:41:04][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[14-07-2025 12:41:04][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[14-07-2025 12:41:04][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[14-07-2025 12:41:04][AP][WARN] ================================
[14-07-2025 15:07:00][AP][WARN] ================================
[14-07-2025 15:07:00][AP][WARN] Message: There was an api-error for RoseResourcepack!
[14-07-2025 15:07:00][AP][WARN] Details: Index 0 out of bounds for length 0
[14-07-2025 15:07:00][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[14-07-2025 15:07:00][AP][WARN] Stacktrace: 
[14-07-2025 15:07:00][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[14-07-2025 15:07:00][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[14-07-2025 15:07:00][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[14-07-2025 15:07:00][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[14-07-2025 15:07:00][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[14-07-2025 15:07:00][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[14-07-2025 15:07:00][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[14-07-2025 15:07:00][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[14-07-2025 15:07:00][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[14-07-2025 15:07:00][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[14-07-2025 15:07:00][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[14-07-2025 15:07:00][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[14-07-2025 15:07:00][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[14-07-2025 15:07:00][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[14-07-2025 15:07:00][AP][WARN] ================================
[14-07-2025 23:43:47][AP][WARN] ================================
[14-07-2025 23:43:47][AP][WARN] Message: There was an api-error for RoseResourcepack!
[14-07-2025 23:43:47][AP][WARN] Details: Index 0 out of bounds for length 0
[14-07-2025 23:43:47][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[14-07-2025 23:43:47][AP][WARN] Stacktrace: 
[14-07-2025 23:43:47][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[14-07-2025 23:43:47][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[14-07-2025 23:43:47][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[14-07-2025 23:43:47][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[14-07-2025 23:43:47][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[14-07-2025 23:43:47][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[14-07-2025 23:43:47][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[14-07-2025 23:43:47][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[14-07-2025 23:43:47][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[14-07-2025 23:43:47][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[14-07-2025 23:43:47][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[14-07-2025 23:43:47][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[14-07-2025 23:43:47][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[14-07-2025 23:43:47][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[14-07-2025 23:43:47][AP][WARN] ================================
[14-07-2025 23:47:48][AP][WARN] ================================
[14-07-2025 23:47:48][AP][WARN] Message: There was an api-error for RoseResourcepack!
[14-07-2025 23:47:48][AP][WARN] Details: Index 0 out of bounds for length 0
[14-07-2025 23:47:48][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[14-07-2025 23:47:48][AP][WARN] Stacktrace: 
[14-07-2025 23:47:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[14-07-2025 23:47:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[14-07-2025 23:47:48][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[14-07-2025 23:47:48][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[14-07-2025 23:47:48][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[14-07-2025 23:47:48][AP][WARN] ================================
[14-07-2025 23:47:48][AP][WARN] ================================
[14-07-2025 23:47:48][AP][WARN] Message: There was an api-error for OpenJS!
[14-07-2025 23:47:48][AP][WARN] Details: Index 0 out of bounds for length 0
[14-07-2025 23:47:48][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[14-07-2025 23:47:48][AP][WARN] Stacktrace: 
[14-07-2025 23:47:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[14-07-2025 23:47:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[14-07-2025 23:47:48][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[14-07-2025 23:47:48][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[14-07-2025 23:47:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[14-07-2025 23:47:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[14-07-2025 23:47:48][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[14-07-2025 23:47:48][AP][WARN] ================================
[15-07-2025 16:47:43][AP][WARN] ================================
[15-07-2025 16:47:43][AP][WARN] Message: There was an api-error for RoseResourcepack!
[15-07-2025 16:47:43][AP][WARN] Details: Index 0 out of bounds for length 0
[15-07-2025 16:47:43][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[15-07-2025 16:47:43][AP][WARN] Stacktrace: 
[15-07-2025 16:47:43][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[15-07-2025 16:47:43][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[15-07-2025 16:47:43][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[15-07-2025 16:47:43][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[15-07-2025 16:47:43][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[15-07-2025 16:47:43][AP][WARN] ================================
[15-07-2025 16:47:43][AP][WARN] ================================
[15-07-2025 16:47:43][AP][WARN] Message: There was an api-error for OpenJS!
[15-07-2025 16:47:43][AP][WARN] Details: Index 0 out of bounds for length 0
[15-07-2025 16:47:43][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[15-07-2025 16:47:43][AP][WARN] Stacktrace: 
[15-07-2025 16:47:43][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[15-07-2025 16:47:43][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[15-07-2025 16:47:43][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[15-07-2025 16:47:43][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[15-07-2025 16:47:43][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[15-07-2025 16:47:43][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[15-07-2025 16:47:43][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[15-07-2025 16:47:43][AP][WARN] ================================
[16-07-2025 03:00:40][AP][WARN] ================================
[16-07-2025 03:00:40][AP][WARN] Message: There was an api-error for RoseResourcepack!
[16-07-2025 03:00:40][AP][WARN] Details: Index 0 out of bounds for length 0
[16-07-2025 03:00:40][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[16-07-2025 03:00:40][AP][WARN] Stacktrace: 
[16-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[16-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[16-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[16-07-2025 03:00:40][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[16-07-2025 03:00:40][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[16-07-2025 03:00:40][AP][WARN] ================================
[16-07-2025 03:00:40][AP][WARN] ================================
[16-07-2025 03:00:40][AP][WARN] Message: There was an api-error for OpenJS!
[16-07-2025 03:00:40][AP][WARN] Details: Index 0 out of bounds for length 0
[16-07-2025 03:00:40][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[16-07-2025 03:00:40][AP][WARN] Stacktrace: 
[16-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[16-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[16-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[16-07-2025 03:00:40][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[16-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[16-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[16-07-2025 03:00:40][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[16-07-2025 03:00:40][AP][WARN] ================================
[17-07-2025 18:49:32][AP][WARN] ================================
[17-07-2025 18:49:32][AP][WARN] Message: There was an api-error for RoseResourcepack!
[17-07-2025 18:49:32][AP][WARN] Details: Index 0 out of bounds for length 0
[17-07-2025 18:49:32][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[17-07-2025 18:49:32][AP][WARN] Stacktrace: 
[17-07-2025 18:49:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[17-07-2025 18:49:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[17-07-2025 18:49:32][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[17-07-2025 18:49:32][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[17-07-2025 18:49:32][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[17-07-2025 18:49:32][AP][WARN] ================================
[17-07-2025 18:49:32][AP][WARN] ================================
[17-07-2025 18:49:32][AP][WARN] Message: There was an api-error for OpenJS!
[17-07-2025 18:49:32][AP][WARN] Details: Index 0 out of bounds for length 0
[17-07-2025 18:49:32][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[17-07-2025 18:49:32][AP][WARN] Stacktrace: 
[17-07-2025 18:49:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[17-07-2025 18:49:32][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[17-07-2025 18:49:32][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[17-07-2025 18:49:32][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[17-07-2025 18:49:32][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[17-07-2025 18:49:32][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[17-07-2025 18:49:32][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[17-07-2025 18:49:32][AP][WARN] ================================
[18-07-2025 03:00:26][AP][WARN] ================================
[18-07-2025 03:00:26][AP][WARN] Message: There was an api-error for RoseResourcepack!
[18-07-2025 03:00:26][AP][WARN] Details: Index 0 out of bounds for length 0
[18-07-2025 03:00:26][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[18-07-2025 03:00:26][AP][WARN] Stacktrace: 
[18-07-2025 03:00:26][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[18-07-2025 03:00:26][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[18-07-2025 03:00:26][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[18-07-2025 03:00:26][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[18-07-2025 03:00:26][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[18-07-2025 03:00:26][AP][WARN] ================================
[18-07-2025 03:00:26][AP][WARN] ================================
[18-07-2025 03:00:26][AP][WARN] Message: There was an api-error for OpenJS!
[18-07-2025 03:00:26][AP][WARN] Details: Index 0 out of bounds for length 0
[18-07-2025 03:00:26][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[18-07-2025 03:00:26][AP][WARN] Stacktrace: 
[18-07-2025 03:00:26][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[18-07-2025 03:00:26][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[18-07-2025 03:00:26][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[18-07-2025 03:00:26][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[18-07-2025 03:00:26][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[18-07-2025 03:00:26][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[18-07-2025 03:00:26][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[18-07-2025 03:00:26][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] Message: There was an api-error for RoseResourcepack!
[19-07-2025 16:43:02][AP][WARN] Details: Index 0 out of bounds for length 0
[19-07-2025 16:43:02][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[19-07-2025 16:43:02][AP][WARN] Stacktrace: 
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[19-07-2025 16:43:02][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[19-07-2025 16:43:02][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] Message: There was an api-error for OpenJS!
[19-07-2025 16:43:02][AP][WARN] Details: Index 0 out of bounds for length 0
[19-07-2025 16:43:02][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[19-07-2025 16:43:02][AP][WARN] Stacktrace: 
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[19-07-2025 16:43:02][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[19-07-2025 16:43:02][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[19-07-2025 16:43:02][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] Message: There was an api-error for RoseResourcepack!
[20-07-2025 21:48:31][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 21:48:31][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 21:48:31][AP][WARN] Stacktrace: 
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 21:48:31][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 21:48:31][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] Message: There was an api-error for OpenJS!
[20-07-2025 21:48:31][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 21:48:31][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 21:48:31][AP][WARN] Stacktrace: 
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 21:48:31][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 21:48:31][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] Message: There was an api-error for RoseResourcepack!
[20-07-2025 23:43:48][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 23:43:48][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 23:43:48][AP][WARN] Stacktrace: 
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 23:43:48][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 23:43:48][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] Message: There was an api-error for OpenJS!
[20-07-2025 23:43:48][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 23:43:48][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 23:43:48][AP][WARN] Stacktrace: 
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 23:43:48][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 23:43:48][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 23:43:48][AP][WARN] ================================
[21-07-2025 13:14:54][AP][WARN] ================================
[21-07-2025 13:14:54][AP][WARN] Message: There was an api-error for RoseResourcepack!
[21-07-2025 13:14:54][AP][WARN] Details: Index 0 out of bounds for length 0
[21-07-2025 13:14:54][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[21-07-2025 13:14:54][AP][WARN] Stacktrace: 
[21-07-2025 13:14:54][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[21-07-2025 13:14:54][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[21-07-2025 13:14:54][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[21-07-2025 13:14:54][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[21-07-2025 13:14:54][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[21-07-2025 13:14:54][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] Message: There was an api-error for OpenJS!
[21-07-2025 13:14:55][AP][WARN] Details: Index 0 out of bounds for length 0
[21-07-2025 13:14:55][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[21-07-2025 13:14:55][AP][WARN] Stacktrace: 
[21-07-2025 13:14:55][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[21-07-2025 13:14:55][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[21-07-2025 13:14:55][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[21-07-2025 13:14:55][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[21-07-2025 13:14:55][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[21-07-2025 13:14:55][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] Message: There was an api-error for RoseResourcepack!
[22-07-2025 03:00:40][AP][WARN] Details: Index 0 out of bounds for length 0
[22-07-2025 03:00:40][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[22-07-2025 03:00:40][AP][WARN] Stacktrace: 
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[22-07-2025 03:00:40][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[22-07-2025 03:00:40][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] Message: There was an api-error for OpenJS!
[22-07-2025 03:00:40][AP][WARN] Details: Index 0 out of bounds for length 0
[22-07-2025 03:00:40][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[22-07-2025 03:00:40][AP][WARN] Stacktrace: 
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[22-07-2025 03:00:40][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[22-07-2025 03:00:40][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] Message: There was an api-error for RoseResourcepack!
[22-07-2025 23:40:33][AP][WARN] Details: Index 0 out of bounds for length 0
[22-07-2025 23:40:33][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[22-07-2025 23:40:33][AP][WARN] Stacktrace: 
[22-07-2025 23:40:33][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[22-07-2025 23:40:33][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[22-07-2025 23:40:33][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[22-07-2025 23:40:33][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[22-07-2025 23:40:33][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[22-07-2025 23:40:33][AP][WARN] ================================
[23-07-2025 11:28:49][AP][WARN] ================================
[23-07-2025 11:28:49][AP][WARN] Message: There was an api-error for RoseResourcepack!
[23-07-2025 11:28:49][AP][WARN] Details: Index 0 out of bounds for length 0
[23-07-2025 11:28:49][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[23-07-2025 11:28:49][AP][WARN] Stacktrace: 
[23-07-2025 11:28:49][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[23-07-2025 11:28:49][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[23-07-2025 11:28:49][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[23-07-2025 11:28:49][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[23-07-2025 11:28:49][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[23-07-2025 11:28:49][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] Message: There was an api-error for RoseResourcepack!
[23-07-2025 22:55:59][AP][WARN] Details: Index 0 out of bounds for length 0
[23-07-2025 22:55:59][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[23-07-2025 22:55:59][AP][WARN] Stacktrace: 
[23-07-2025 22:55:59][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[23-07-2025 22:55:59][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[23-07-2025 22:55:59][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[23-07-2025 22:55:59][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[23-07-2025 22:55:59][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[23-07-2025 22:55:59][AP][WARN] ================================
