[22:56:01] [ServerMain/INFO]: [bootstrap] Running Java 21 (OpenJDK 64-Bit Server VM 21.0.7+6-LTS; Eclipse Adoptium Temurin-21.0.7+6) on Linux 6.15.6-zen1-1-zen (amd64)
[22:56:01] [ServerMain/INFO]: [bootstrap] Loading Purpur 1.21.7-2477-HEAD@60bdf1c (2025-07-18T03:06:59Z) for Minecraft 1.21.7
[22:56:02] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[22:56:02] [ServerMain/INFO]: [PluginInitializerManager] Initialized 16 plugins
[22:56:02] [ServerMain/INFO]: [PluginInitializerManager] Paper plugins (1):
 - ServiceIO (3.0.0-pre6)
[22:56:02] [ServerMain/INFO]: [PluginInitializerManager] Bukkit plugins (15):
 - Actions (2.74.1), AutoPluginLoader (1.5.1), CustomCrops (3.6.42), EcoArmor (8.75.1), EcoEnchants (12.23.1), EcoItems (5.63.1), EcoMobs (10.21.1), FreedomChat (1.7.5), ImageEmojis (1.5.2), LuckPerms (5.5.9), Oraxen (1.191.0), PlaceholderAPI (2.11.7-DEV-212), ProtocolLib (5.4.0-SNAPSHOT-753), RoseResourcepack (3.3.4), eco (6.76.2)
[22:56:06] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[22:56:07] [ServerMain/INFO]: Loaded 1407 recipes
[22:56:07] [ServerMain/INFO]: Loaded 1520 advancements
[22:56:07] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Initialising converters for DataConverter...
[22:56:07] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Finished initialising converters for DataConverter in 253.8ms
[22:56:07] [Server thread/INFO]: Starting minecraft server version 1.21.7
[22:56:07] [Server thread/INFO]: Loading properties
[22:56:07] [Server thread/INFO]: This server is running Purpur version 1.21.7-2477-HEAD@60bdf1c (2025-07-18T03:06:59Z) (Implementing API version 1.21.7-R0.1-SNAPSHOT)
[22:56:08] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[22:56:08] [Server thread/INFO]: Server Ping Player Sample Count: 12
[22:56:08] [Server thread/INFO]: Using 4 threads for Netty based IO
[22:56:08] [Server thread/INFO]: [MoonriseCommon] Paper is using 3 worker threads, 1 I/O threads
[22:56:08] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true
[22:56:08] [Server thread/INFO]: Default game type: SURVIVAL
[22:56:08] [Server thread/INFO]: Generating keypair
[22:56:08] [Server thread/INFO]: Starting Minecraft server on *:25565
[22:56:08] [Server thread/INFO]: Using epoll channel type
[22:56:08] [Server thread/INFO]: Paper: Using libdeflate (Linux x86_64) compression from Velocity.
[22:56:08] [Server thread/INFO]: Paper: Using OpenSSL 3.x.x (Linux x86_64) cipher from Velocity.
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loading 10 libraries... please wait
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/springframework/spring-expression/6.0.6/spring-expression-6.0.6.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/springframework/spring-core/6.0.6/spring-core-6.0.6.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/springframework/spring-jcl/6.0.6/spring-jcl-6.0.6.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/apache/httpcomponents/httpmime/4.5.13/httpmime-4.5.13.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/commons-logging/commons-logging/1.2/commons-logging-1.2.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/commons-codec/commons-codec/1.11/commons-codec-1.11.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/joml/joml/1.10.5/joml-1.10.5.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-text-minimessage/4.17.0/adventure-text-minimessage-4.17.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-api/4.17.0/adventure-api-4.17.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-key/4.17.0/adventure-key-4.17.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/examination-api/1.3.0/examination-api-1.3.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/examination-string/1.3.0/examination-string-1.3.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/jetbrains/annotations/24.1.0/annotations-24.1.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-text-serializer-plain/4.17.0/adventure-text-serializer-plain-4.17.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-text-serializer-ansi/4.17.0/adventure-text-serializer-ansi-4.17.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/ansi/1.0.3/ansi-1.0.3.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-platform-bukkit/4.3.4/adventure-platform-bukkit-4.3.4.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-platform-api/4.3.4/adventure-platform-api-4.3.4.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-text-serializer-bungeecord/4.3.4/adventure-text-serializer-bungeecord-4.3.4.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-text-serializer-legacy/4.13.1/adventure-text-serializer-legacy-4.13.1.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-nbt/4.13.1/adventure-nbt-4.13.1.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-text-serializer-gson/4.13.1/adventure-text-serializer-gson-4.13.1.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-text-serializer-gson-legacy-impl/4.13.1/adventure-text-serializer-gson-legacy-impl-4.13.1.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-platform-facet/4.3.4/adventure-platform-facet-4.3.4.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/net/kyori/adventure-platform-viaversion/4.3.4/adventure-platform-viaversion-4.3.4.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar
[22:56:08] [Server thread/INFO]: [SpigotLibraryLoader] [Oraxen] Loaded library /run/media/glitchy/Data/Projects/mc_server/src/libraries/gs/mclo/java/2.2.1/java-2.2.1.jar
[22:56:09] [Server thread/INFO]: [eco] Initializing eco
[22:56:09] [Server thread/INFO]: [EcoItems] Initializing EcoItems
[22:56:11] [Server thread/INFO]: [EcoMobs] Initializing EcoMobs
[22:56:12] [Server thread/INFO]: [EcoEnchants] Initializing EcoEnchants
[22:56:12] [Server thread/INFO]: [EcoArmor] Initializing EcoArmor
[22:56:14] [Server thread/INFO]: [Actions] Initializing Actions
[22:56:15] [Server thread/INFO]: [LuckPerms] Loading server plugin LuckPerms v5.5.9
[22:56:15] [Server thread/INFO]: [LuckPerms] Loading configuration...
[22:56:16] [Server thread/INFO]: [ProtocolLib] Loading server plugin ProtocolLib v5.4.0-SNAPSHOT-753
[22:56:16] [Server thread/WARN]: [ProtocolLib] Version (MC: 1.21.7) has not yet been tested! Proceed with caution.
[22:56:16] [Server thread/INFO]: [PlaceholderAPI] Loading server plugin PlaceholderAPI v2.11.7-DEV-212
[22:56:16] [Server thread/INFO]: [ServiceIO] Loading server plugin ServiceIO v3.0.0-pre6
[22:56:16] [Server thread/INFO]: [Oraxen] Loading server plugin Oraxen v1.191.0
[22:56:16] [Server thread/INFO]: [eco] Loading server plugin eco v6.76.2
[22:56:16] [Server thread/INFO]: [EcoItems] Loading server plugin EcoItems v5.63.1
[22:56:16] [Server thread/INFO]: [libreforge] Initializing libreforge
[22:56:16] [Server thread/INFO]: [libreforge] Loading server plugin libreforge v4.76.1
[22:56:16] [Server thread/INFO]: [AutoPluginLoader] Loading server plugin AutoPluginLoader v1.5.1
[22:56:16] [Server thread/INFO]: [EcoMobs] Loading server plugin EcoMobs v10.21.1
[22:56:16] [Server thread/INFO]: [EcoEnchants] Loading server plugin EcoEnchants v12.23.1
[22:56:16] [ForkJoinPool.commonPool-worker-1/WARN]: [ServiceIO] You are running a snapshot version of ServiceIO
[22:56:16] [Server thread/INFO]: [EcoArmor] Loading server plugin EcoArmor v8.75.1
[22:56:16] [Server thread/INFO]: [CustomCrops] Loading server plugin CustomCrops v3.6.42
[22:56:17] [Server thread/INFO]: [FreedomChat] Loading server plugin FreedomChat v1.7.5
[22:56:17] [Server thread/INFO]: [RoseResourcepack] Loading server plugin RoseResourcepack v3.3.4
[22:56:17] [Server thread/INFO]: [Actions] Loading server plugin Actions v2.74.1
[22:56:17] [Server thread/INFO]: [ImageEmojis] Loading server plugin ImageEmojis v1.5.2
[22:56:17] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[22:56:17] [Server thread/INFO]: [LuckPerms] Enabling LuckPerms v5.5.9
[22:56:17] [Server thread/INFO]:         __    
[22:56:17] [Server thread/INFO]:   |    |__)   LuckPerms v5.5.9
[22:56:17] [Server thread/INFO]:   |___ |      Running on Bukkit - Purpur
[22:56:17] [Server thread/INFO]: 
[22:56:17] [Server thread/INFO]: [LuckPerms] Loading storage provider... [H2]
[22:56:17] [Server thread/INFO]: [LuckPerms] Loading internal permission managers...
[22:56:17] [Server thread/INFO]: [LuckPerms] Performing initial data load...
[22:56:18] [Server thread/INFO]: [LuckPerms] Successfully enabled. (took 918ms)
[22:56:18] [Server thread/INFO]: [ProtocolLib] Enabling ProtocolLib v5.4.0-SNAPSHOT-753
[22:56:18] [Server thread/INFO]: [ServiceIO] Enabling ServiceIO v3.0.0-pre6
[22:56:18] [Server thread/INFO]: [ServiceIO] Initialized support for LuckPerms as PermissionController (Highest)
[22:56:18] [Server thread/INFO]: [ServiceIO] Initialized support for LuckPerms Groups as GroupController (Highest)
[22:56:18] [Server thread/INFO]: [ServiceIO] Initialized support for LuckPerms Chat as ChatController (Highest)
[22:56:18] [Server thread/INFO]: [ServiceIO] Registered placeholders for LuckPermsChatController (ChatController)
[22:56:18] [Server thread/INFO]: [ServiceIO] Registered placeholders for LuckPermsGroupController (GroupController)
[22:56:18] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: serviceio [3.0.0-pre6]
[22:56:18] [Server thread/INFO]: [LuckPerms] Registered Vault permission & chat hook.
[22:56:18] [Server thread/INFO]: [eco] Enabling eco v6.76.2
[22:56:18] [Server thread/INFO]: [eco] Loading eco
[22:56:18] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: eco [6.76.2]
[22:56:18] [Server thread/INFO]: [eco] Loaded integrations: Oraxen, PlaceholderAPI
[22:56:18] [Server thread/INFO]: [eco] Scanning for conflicts...
[22:56:18] [Server thread/INFO]: [eco] No conflicts found!
[22:56:18] [Server thread/INFO]: [EcoEnchants] Enabling EcoEnchants v12.23.1
[22:56:18] [Server thread/INFO]: [EcoEnchants] Loading EcoEnchants
[22:56:18] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoenchants [12.23.1]
[22:56:18] [Server thread/INFO]: Preparing level "world"
[22:56:18] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[22:56:18] [Server thread/INFO]: Preparing spawn area: 0%
[22:56:19] [Server thread/INFO]: Preparing spawn area: 57%
[22:56:19] [Server thread/INFO]: Time elapsed: 651 ms
[22:56:19] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[22:56:19] [Server thread/INFO]: Preparing spawn area: 0%
[22:56:19] [Server thread/INFO]: Time elapsed: 79 ms
[22:56:19] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[22:56:19] [Server thread/INFO]: Preparing spawn area: 0%
[22:56:19] [Server thread/INFO]: Time elapsed: 62 ms
[22:56:19] [Server thread/INFO]: [PlaceholderAPI] Enabling PlaceholderAPI v2.11.7-DEV-212
[22:56:19] [Server thread/INFO]: [PlaceholderAPI] Fetching available expansion information...
[22:56:19] [Server thread/INFO]: [Oraxen] Enabling Oraxen v1.191.0
[22:56:19] [Server thread/INFO]: Oraxen | Papers block-updates.disable-noteblock-updates is not enabled.
[22:56:19] [Server thread/INFO]: Oraxen | It is recommended to enable this setting for improved performance and prevent bugs with noteblocks
[22:56:19] [Server thread/INFO]: Oraxen | Otherwise Oraxen needs to listen to very taxing events, which also introduces some bugs
[22:56:19] [Server thread/INFO]: Oraxen | You can enable this setting in ServerFolder/config/paper-global.yml
[22:56:19] [Server thread/INFO]: Oraxen | Papers block-updates.disable-tripwire-updates is not enabled.
[22:56:19] [Server thread/INFO]: Oraxen | It is recommended to enable this setting for improved performance and prevent bugs with tripwires
[22:56:19] [Server thread/INFO]: Oraxen | Otherwise Oraxen needs to listen to very taxing events, which also introduces some bugs
[22:56:19] [Server thread/INFO]: Oraxen | You can enable this setting in ServerFolder/config/paper-global.yml
[22:56:19] [Server thread/INFO]: Oraxen | The texture specified for farmer_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for explorer_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for knight_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for lord_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for prince_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for king_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for hero_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for god_tag does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | The texture specified for heart does not exist. This will break all your glyphs.
[22:56:20] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:56:20] [Server thread/INFO]: Oraxen | Slicing gui-textures to 1.20.2-format...
[22:56:20] [Server thread/INFO]: Oraxen | Successfully sliced gui-textures for 1.20.2
[22:56:20] [Server thread/INFO]: Oraxen | Converting global lang file to individual language files...
[22:56:20] [Server thread/INFO]: Oraxen | Verifying formatting for textures and models...
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/weed_leaf.json: default/weed/leaf
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/amethyst_hammer.json: default/amethyst_hammer
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_boots.json: default/armors/emerald_boots
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby.json: default/ruby
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_chestplate.json: default/armors/obsidian_chestplate
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/brunnera.json: default/flowers/brunnera
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/onyx.json: default/onyx
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/grape.json: default/grape
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_ore.json: default/ruby_ore
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/glass_sword.json: default/glass_sword
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/weed_seed.json: default/weed/seed
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/grape_seeds.json: default/grape_seeds
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_boots.json: default/armors/ruby_boots
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_leggings.json: default/armors/obsidian_leggings
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/daffodil.json: default/flowers/daffodil
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_chestplate.json: default/armors/ruby_chestplate
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_helmet.json: default/armors/ruby_helmet
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/onyx_hammer.json: default/onyx_hammer
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/amethyst_ore.json: default/amethyst_ore
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/orax_ore.json: default/orax_ore
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_helmet.json: default/armors/emerald_helmet
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/iron_cog.json: default/iron_cog
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_hammer.json: default/emerald_hammer
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/welcome_disk.json: default/welcome_disk
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/amethyst.json: default/amethyst
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/fire_hammer.json: default/fire_hammer
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/onyx_ore.json: default/onyx_ore
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/orax.json: default/orax
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/dailily.json: default/flowers/dailily
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_helmet.json: default/armors/obsidian_helmet
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/miner_sandwitch.json: default/sandwitch
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_pickaxe.json: default/obsidian_pickaxe
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/diamond_cog.json: default/diamond_cog
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/orax_hammer.json: default/orax_hammer
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_chestplate.json: default/armors/emerald_chestplate
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_boots.json: default/armors/obsidian_boots
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_leggings.json: default/armors/emerald_leggings
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/wooden_hammer.json: hammers/wooden_hammer
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/gold_cog.json: default/gold_cog
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_leggings.json: default/armors/ruby_leggings
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_sword.json: default/obsidian_sword
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/iron_serpe.json: default/iron_serpe
[22:56:20] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:56:20] [Server thread/INFO]: Oraxen | Pack contains malformed texture(s) and/or model(s)
[22:56:20] [Server thread/INFO]: Oraxen | These need to be fixed, otherwise the resourcepack will be broken
[22:56:20] [Server thread/INFO]: Oraxen | Generating atlas-file for 1.19.3+ Resource Pack format
[22:56:20] [Server thread/INFO]: Oraxen | Attempting to exclude malformed textures from atlas-file
[22:56:20] [Server thread/INFO]: Oraxen | No duplicate font files found!
[22:56:20] [Server thread/INFO]: Oraxen | c
[22:56:20] [Server thread/INFO]: Oraxen | c
[22:56:20] [Server thread/INFO]: Oraxen | Successfully loaded on Garuda Linux Broadwing ("Broadwing")
[22:56:20] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: oraxen [1.191.0]
[22:56:20] [Server thread/INFO]: Oraxen | Plugin "PlaceholderAPI" detected, enabling hooks
[22:56:20] [Server thread/INFO]: Oraxen | This is a compiled version of Oraxen.
[22:56:20] [Server thread/INFO]: Oraxen | Compiled versions come without Default assets and support is not provided.
[22:56:20] [Server thread/INFO]: Oraxen | Consider purchasing Oraxen on SpigotMC or Polymart for access to the full version.
[22:56:20] [Server thread/INFO]: [EcoItems] Enabling EcoItems v5.63.1
[22:56:20] [Server thread/INFO]: [EcoItems] Loading EcoItems
[22:56:20] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoitems [5.63.1]
[22:56:20] [Server thread/INFO]: [AutoPluginLoader] Enabling AutoPluginLoader v1.5.1
[22:56:20] [Server thread/INFO]: [AutoPluginLoader] Loading libraries...
[22:56:20] [Server thread/INFO]: [AutoPluginLoader] Loaded successfully. (Took 10ms)
[22:56:20] [Server thread/INFO]: [EcoMobs] Enabling EcoMobs v10.21.1
[22:56:20] [Server thread/INFO]: [EcoMobs] Loading EcoMobs
[22:56:20] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecomobs [10.21.1]
[22:56:20] [Server thread/INFO]: [EcoArmor] Enabling EcoArmor v8.75.1
[22:56:20] [Server thread/INFO]: [EcoArmor] Loading EcoArmor
[22:56:20] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoarmor [8.75.1]
[22:56:20] [Server thread/INFO]: [CustomCrops] Enabling CustomCrops v3.6.42
[22:56:20] [Server thread/INFO]: [CustomCrops] Oraxen hooked!
[22:56:20] [Server thread/INFO]: [CustomCrops] Vault hooked!
[22:56:20] [Server thread/INFO]: [CustomCrops] PlaceholderAPI hooked!
[22:56:20] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: customcrops [3.6]
[22:56:21] [Server thread/WARN]: [CustomCrops] en_us.yml not exists, using en.yml as default locale.
[22:56:21] [Server thread/INFO]: [FreedomChat] Enabling FreedomChat v1.7.5
[22:56:21] [Server thread/INFO]: [RoseResourcepack] Enabling RoseResourcepack v3.3.4
[22:56:21] [Server thread/INFO]: [Actions] Enabling Actions v2.74.1
[22:56:21] [Server thread/INFO]: [Actions] Loading Actions
[22:56:21] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: actions [2.74.1]
[22:56:21] [ForkJoinPool.commonPool-worker-3/WARN]: Duplicate entry found and skipped: pack.mcmeta
[22:56:21] [ForkJoinPool.commonPool-worker-3/WARN]: Duplicate entry found and skipped: pack.png
[22:56:21] [Server thread/INFO]: [ImageEmojis] Enabling ImageEmojis v1.5.2
[22:56:21] [Server thread/INFO]: [ImageEmojis] Enforcement policy set to NONE: No need to start the resource pack HTTP server.
[22:56:21] [Server thread/INFO]: [libreforge] Enabling libreforge v4.76.1
[22:56:21] [Server thread/INFO]: [libreforge] Loading libreforge
[22:56:21] [ForkJoinPool.commonPool-worker-3/INFO]: Resource pack main successfully packed into archive
[22:56:21] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: libreforge [4.76.1]
[22:56:21] [Server thread/INFO]: [libreforge] Loaded integrations: CustomCrops
[22:56:21] [Server thread/INFO]: [libreforge] 
[22:56:21] [Server thread/INFO]: [libreforge] Hey, what's this plugin doing here? I didn't install it!
[22:56:21] [Server thread/INFO]: [libreforge] libreforge is the effects system for plugins like EcoEnchants,
[22:56:21] [Server thread/INFO]: [libreforge] EcoJobs, EcoItems, etc. If you're looking for config options for
[22:56:21] [Server thread/INFO]: [libreforge] things like cooldown messages, lrcdb, and stuff like that, you'll
[22:56:21] [Server thread/INFO]: [libreforge] find it under /plugins/libreforge
[22:56:21] [Server thread/INFO]: [libreforge] 
[22:56:21] [Server thread/INFO]: [libreforge] Don't worry about updating libreforge, it's handled automatically!
[22:56:21] [Server thread/INFO]: [libreforge] 
[22:56:21] [customcrops-worker-3/WARN]: [CustomCrops] Update is available: https://github.com/Xiao-MoMi/Custom-Crops/
[22:56:21] [Server thread/INFO]: [spark] Starting background profiler...
[22:56:21] [Server thread/INFO]: [PlaceholderAPI] Placeholder expansion registration initializing...
[22:56:21] [Server thread/INFO]: 0 placeholder hook(s) registered!
[22:56:21] [Server thread/INFO]: Done preparing level "world" (3.607s)
[22:56:21] [Server thread/INFO]: Running delayed init tasks
[22:56:21] [Server thread/INFO]: [CustomCrops] Registry access has been frozen
[22:56:21] [Server thread/INFO]: Check for updates...
[22:56:22] [Server thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[22:56:22] [Server thread/INFO]: Done (21.753s)! For help, type "help"
[22:56:22] [Server thread/INFO]: [eco] Loaded eco
[22:56:22] [Server thread/WARN]: [EcoEnchants] 19 enchantments were not loaded because they need EcoSkills to be installed!
[22:56:22] [Server thread/WARN]: [EcoEnchants] Either download EcoSkills or delete the folder at /plugins/EcoEnchants/enchants/ecoskills to remove this message
[22:56:22] [Server thread/WARN]: [EcoEnchants] 1 enchantments were not loaded because they need EcoJobs to be installed!
[22:56:22] [Server thread/WARN]: [EcoEnchants] Either download EcoJobs or delete the folder at /plugins/EcoEnchants/enchants/ecojobs to remove this message
[22:56:22] [Server thread/WARN]: [EcoEnchants] 1 enchantments were not loaded because they need EcoPets to be installed!
[22:56:22] [Server thread/WARN]: [EcoEnchants] Either download EcoPets or delete the folder at /plugins/EcoEnchants/enchants/ecopets to remove this message
[22:56:22] [Server thread/INFO]: [EcoEnchants] Loaded EcoEnchants
[22:56:22] [Server thread/INFO]: [EcoItems] Loaded EcoItems
[22:56:22] [Server thread/INFO]: [EcoMobs] Loaded EcoMobs
[22:56:22] [Server thread/INFO]: [EcoArmor] Loaded EcoArmor
[22:56:22] [Server thread/INFO]: [Actions] Loaded Actions
[22:56:22] [Server thread/INFO]: [libreforge] Loaded libreforge
[22:56:45] [User Authenticator #0/INFO]: UUID of player Cunyarc is 66e9f0fe-0991-4fef-b0c2-5bd0354aa36f
[22:56:45] [Server thread/INFO]: [HorriblePlayerLoginEventHack] You have plugins listening to the PlayerLoginEvent, this will cause re-configuration APIs to be unavailable: [eco, LuckPerms, CustomCrops, ProtocolLib]
[22:56:45] [Server thread/INFO]: Cunyarc joined the game
[22:56:46] [Server thread/INFO]: Cunyarc[/127.0.0.1:40944] logged in with entity id 4 at ([world]-1022.3256412638307, 87.0, -755.4922719557604)
[22:57:13] [Server thread/INFO]: Cunyarc issued server command: /oraxen give @p wooden_hammer
[22:58:37] [Server thread/INFO]: Cunyarc issued server command: /roseresourcepack reset
[22:59:29] [Server thread/INFO]: Cunyarc issued server command: /oraxen reload all
[22:59:29] [Server thread/INFO]: Oraxen | Papers block-updates.disable-noteblock-updates is not enabled.
[22:59:29] [Server thread/INFO]: Oraxen | It is recommended to enable this setting for improved performance and prevent bugs with noteblocks
[22:59:29] [Server thread/INFO]: Oraxen | Otherwise Oraxen needs to listen to very taxing events, which also introduces some bugs
[22:59:29] [Server thread/INFO]: Oraxen | You can enable this setting in ServerFolder/config/paper-global.yml
[22:59:29] [Server thread/INFO]: Oraxen | Papers block-updates.disable-tripwire-updates is not enabled.
[22:59:29] [Server thread/INFO]: Oraxen | It is recommended to enable this setting for improved performance and prevent bugs with tripwires
[22:59:29] [Server thread/INFO]: Oraxen | Otherwise Oraxen needs to listen to very taxing events, which also introduces some bugs
[22:59:29] [Server thread/INFO]: Oraxen | You can enable this setting in ServerFolder/config/paper-global.yml
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | NMSHandler is null - some components won't work properly
[22:59:29] [Server thread/INFO]: Oraxen | Updating all items in player-inventories...
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for farmer_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for explorer_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for knight_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for lord_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for prince_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for king_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for hero_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for god_tag does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | The texture specified for heart does not exist. This will break all your glyphs.
[22:59:29] [Server thread/INFO]: Oraxen | It has been temporarily set to a placeholder image. You should edit this in the glyph config.
[22:59:29] [Server thread/INFO]: Oraxen | Slicing gui-textures to 1.20.2-format...
[22:59:29] [Server thread/INFO]: Oraxen | Successfully sliced gui-textures for 1.20.2
[22:59:29] [Server thread/INFO]: Oraxen | Converting global lang file to individual language files...
[22:59:29] [Server thread/INFO]: Oraxen | Verifying formatting for textures and models...
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/weed_seed.json: default/weed/seed
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/iron_serpe.json: default/iron_serpe
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/weed_leaf.json: default/weed/leaf
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/glass_sword.json: default/glass_sword
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/fire_hammer.json: default/fire_hammer
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_boots.json: default/armors/emerald_boots
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_leggings.json: default/armors/ruby_leggings
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/orax_hammer.json: default/orax_hammer
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/daffodil.json: default/flowers/daffodil
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_chestplate.json: default/armors/obsidian_chestplate
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/amethyst_ore.json: default/amethyst_ore
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_pickaxe.json: default/obsidian_pickaxe
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_leggings.json: default/armors/emerald_leggings
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_chestplate.json: default/armors/emerald_chestplate
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/diamond_cog.json: default/diamond_cog
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_leggings.json: default/armors/obsidian_leggings
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_chestplate.json: default/armors/ruby_chestplate
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_helmet.json: default/armors/ruby_helmet
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/welcome_disk.json: default/welcome_disk
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_ore.json: default/ruby_ore
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_boots.json: default/armors/obsidian_boots
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/onyx_ore.json: default/onyx_ore
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/grape.json: default/grape
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/amethyst_hammer.json: default/amethyst_hammer
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/amethyst.json: default/amethyst
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/orax.json: default/orax
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby.json: default/ruby
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/grape_seeds.json: default/grape_seeds
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/ruby_boots.json: default/armors/ruby_boots
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/iron_cog.json: default/iron_cog
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/orax_ore.json: default/orax_ore
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_hammer.json: default/emerald_hammer
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_sword.json: default/obsidian_sword
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/brunnera.json: default/flowers/brunnera
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/obsidian_helmet.json: default/armors/obsidian_helmet
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/onyx.json: default/onyx
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/miner_sandwitch.json: default/sandwitch
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/emerald_helmet.json: default/armors/emerald_helmet
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/dailily.json: default/flowers/dailily
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/onyx_hammer.json: default/onyx_hammer
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Found invalid texture-path inside model-file assets/minecraft/models/gold_cog.json: default/gold_cog
[22:59:29] [Server thread/INFO]: Oraxen | Verify that you have a texture in said path.
[22:59:29] [Server thread/INFO]: Oraxen | Pack contains malformed texture(s) and/or model(s)
[22:59:29] [Server thread/INFO]: Oraxen | These need to be fixed, otherwise the resourcepack will be broken
[22:59:29] [Server thread/INFO]: Oraxen | Generating atlas-file for 1.19.3+ Resource Pack format
[22:59:29] [Server thread/INFO]: Oraxen | Attempting to exclude malformed textures from atlas-file
[22:59:29] [Server thread/INFO]: Oraxen | No duplicate font files found!
[22:59:35] [Server thread/INFO]: Cunyarc issued server command: /oraxen give @p wooden_hammer
[22:59:44] [Server thread/INFO]: Cunyarc issued server command: /roseresourcepack reload
[22:59:44] [ForkJoinPool.commonPool-worker-2/WARN]: Duplicate entry found and skipped: assets/minecraft/font/default.json
[22:59:44] [ForkJoinPool.commonPool-worker-2/WARN]: Duplicate entry found and skipped: pack.mcmeta
[22:59:44] [ForkJoinPool.commonPool-worker-2/WARN]: Duplicate entry found and skipped: pack.png
[22:59:44] [ForkJoinPool.commonPool-worker-2/WARN]: Duplicate entry found and skipped: pack.mcmeta
[22:59:44] [ForkJoinPool.commonPool-worker-2/WARN]: Duplicate entry found and skipped: pack.png
[22:59:44] [ForkJoinPool.commonPool-worker-2/INFO]: Resource pack main successfully packed into archive
[22:59:49] [Server thread/INFO]: Cunyarc issued server command: /roseresourcepack zip
[22:59:55] [Server thread/INFO]: Cunyarc issued server command: /roseresourcepack zip main
[22:59:55] [ForkJoinPool.commonPool-worker-1/WARN]: Duplicate entry found and skipped: assets/minecraft/font/default.json
[22:59:55] [ForkJoinPool.commonPool-worker-1/WARN]: Duplicate entry found and skipped: pack.mcmeta
[22:59:55] [ForkJoinPool.commonPool-worker-1/WARN]: Duplicate entry found and skipped: pack.png
[22:59:55] [ForkJoinPool.commonPool-worker-1/WARN]: Duplicate entry found and skipped: pack.mcmeta
[22:59:55] [ForkJoinPool.commonPool-worker-1/WARN]: Duplicate entry found and skipped: pack.png
[23:00:02] [Server thread/INFO]: Cunyarc issued server command: /roseresourcepack texture Cunyarc
[23:00:05] [Server thread/INFO]: Cunyarc issued server command: /roseresourcepack texture Cunyarc main
[23:00:14] [Server thread/INFO]: Cunyarc issued server command: /oraxen give @p wooden_hammer
[23:01:53] [Server thread/INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden
