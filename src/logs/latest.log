[03:00:42] [ServerMain/INFO]: [bootstrap] Running Java 21 (OpenJDK 64-Bit Server VM 21.0.7+6-LTS; Eclipse Adoptium Temurin-21.0.7+6) on Linux 6.15.6-zen1-1-zen (amd64)
[03:00:42] [ServerMain/INFO]: [bootstrap] Loading Purpur 1.21.7-2477-HEAD@60bdf1c (2025-07-18T03:06:59Z) for Minecraft 1.21.7
[03:00:42] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[03:00:42] [ServerMain/INFO]: [PluginInitializerManager] Initialized 16 plugins
[03:00:42] [ServerMain/INFO]: [PluginInitializerManager] Paper plugins (1):
 - ServiceIO (3.0.0-pre1)
[03:00:42] [ServerMain/INFO]: [PluginInitializerManager] Bukkit plugins (15):
 - Actions (2.74.1), AutoPluginLoader (1.5.1), CustomCrops (3.6.42), EcoArmor (8.75.1), EcoEnchants (12.23.1), EcoItems (5.63.1), EcoMobs (10.21.1), FreedomChat (1.7.5), ImageEmojis (1.5.2), LuckPerms (5.5.9), OpenJS (1.1.0), PlaceholderAPI (2.11.7-DEV-212), ProtocolLib (5.4.0-SNAPSHOT-753), RoseResourcepack (3.3.4), eco (6.76.2)
[03:00:46] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[03:00:47] [ServerMain/INFO]: Loaded 1407 recipes
[03:00:47] [ServerMain/INFO]: Loaded 1520 advancements
[03:00:47] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Initialising converters for DataConverter...
[03:00:47] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Finished initialising converters for DataConverter in 208.0ms
[03:00:47] [Server thread/INFO]: Starting minecraft server version 1.21.7
[03:00:47] [Server thread/INFO]: Loading properties
[03:00:47] [Server thread/INFO]: This server is running Purpur version 1.21.7-2477-HEAD@60bdf1c (2025-07-18T03:06:59Z) (Implementing API version 1.21.7-R0.1-SNAPSHOT)
[03:00:47] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[03:00:47] [Server thread/INFO]: Server Ping Player Sample Count: 12
[03:00:47] [Server thread/INFO]: Using 4 threads for Netty based IO
[03:00:47] [Server thread/INFO]: [MoonriseCommon] Paper is using 3 worker threads, 1 I/O threads
[03:00:47] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true
[03:00:48] [Server thread/INFO]: Default game type: SURVIVAL
[03:00:48] [Server thread/INFO]: Generating keypair
[03:00:48] [Server thread/INFO]: Starting Minecraft server on *:25565
[03:00:48] [Server thread/INFO]: Using epoll channel type
[03:00:48] [Server thread/INFO]: Paper: Using libdeflate (Linux x86_64) compression from Velocity.
[03:00:48] [Server thread/INFO]: Paper: Using OpenSSL 3.x.x (Linux x86_64) cipher from Velocity.
[03:00:48] [Server thread/INFO]: [eco] Initializing eco
[03:00:48] [Server thread/INFO]: [EcoItems] Initializing EcoItems
[03:00:50] [Server thread/INFO]: [EcoMobs] Initializing EcoMobs
[03:00:52] [Server thread/INFO]: [EcoEnchants] Initializing EcoEnchants
[03:00:52] [Server thread/INFO]: [EcoArmor] Initializing EcoArmor
[03:00:53] [Server thread/INFO]: [Actions] Initializing Actions
[03:00:55] [Server thread/INFO]: [LuckPerms] Loading server plugin LuckPerms v5.5.9
[03:00:55] [Server thread/INFO]: [LuckPerms] Loading configuration...
[03:00:55] [Server thread/INFO]: [ProtocolLib] Loading server plugin ProtocolLib v5.4.0-SNAPSHOT-753
[03:00:55] [Server thread/WARN]: [ProtocolLib] Version (MC: 1.21.7) has not yet been tested! Proceed with caution.
[03:00:55] [Server thread/INFO]: [PlaceholderAPI] Loading server plugin PlaceholderAPI v2.11.7-DEV-212
[03:00:55] [Server thread/INFO]: [ServiceIO] Loading server plugin ServiceIO v3.0.0-pre1
[03:00:55] [Server thread/INFO]: [eco] Loading server plugin eco v6.76.2
[03:00:55] [Server thread/INFO]: [EcoItems] Loading server plugin EcoItems v5.63.1
[03:00:55] [Server thread/INFO]: [libreforge] Initializing libreforge
[03:00:55] [Server thread/INFO]: [libreforge] Loading server plugin libreforge v4.76.1
[03:00:55] [Server thread/INFO]: [AutoPluginLoader] Loading server plugin AutoPluginLoader v1.5.1
[03:00:55] [Server thread/INFO]: [EcoMobs] Loading server plugin EcoMobs v10.21.1
[03:00:55] [Server thread/INFO]: [OpenJS] Loading server plugin OpenJS v1.1.0
[03:00:55] [Server thread/INFO]: [EcoEnchants] Loading server plugin EcoEnchants v12.23.1
[03:00:56] [ForkJoinPool.commonPool-worker-1/INFO]: [ServiceIO] You are running the latest version of ServiceIO
[03:00:56] [Server thread/INFO]: [EcoArmor] Loading server plugin EcoArmor v8.75.1
[03:00:56] [Server thread/INFO]: [CustomCrops] Loading server plugin CustomCrops v3.6.42
[03:00:56] [Server thread/INFO]: [FreedomChat] Loading server plugin FreedomChat v1.7.5
[03:00:56] [Server thread/INFO]: [RoseResourcepack] Loading server plugin RoseResourcepack v3.3.4
[03:00:56] [Server thread/INFO]: [Actions] Loading server plugin Actions v2.74.1
[03:00:56] [Server thread/INFO]: [ImageEmojis] Loading server plugin ImageEmojis v1.5.2
[03:00:56] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[03:00:56] [Server thread/INFO]: [LuckPerms] Enabling LuckPerms v5.5.9
[03:00:56] [Server thread/INFO]:         __    
[03:00:56] [Server thread/INFO]:   |    |__)   LuckPerms v5.5.9
[03:00:56] [Server thread/INFO]:   |___ |      Running on Bukkit - Purpur
[03:00:56] [Server thread/INFO]: 
[03:00:56] [Server thread/INFO]: [LuckPerms] Loading storage provider... [H2]
[03:00:57] [Server thread/INFO]: [LuckPerms] Loading internal permission managers...
[03:00:57] [Server thread/INFO]: [LuckPerms] Performing initial data load...
[03:00:57] [Server thread/INFO]: [LuckPerms] Successfully enabled. (took 724ms)
[03:00:57] [Server thread/INFO]: [ProtocolLib] Enabling ProtocolLib v5.4.0-SNAPSHOT-753
[03:00:57] [Server thread/INFO]: [ServiceIO] Enabling ServiceIO v3.0.0-pre1
[03:00:57] [Server thread/INFO]: [ServiceIO] Initialized support for LuckPerms as PermissionController (Highest)
[03:00:57] [Server thread/INFO]: [ServiceIO] Initialized support for LuckPerms Groups as GroupController (Highest)
[03:00:57] [Server thread/INFO]: [ServiceIO] Initialized support for LuckPerms Chat as ChatController (Highest)
[03:00:57] [Server thread/INFO]: [ServiceIO] Registered placeholders for LuckPermsChatController (ChatController)
[03:00:57] [Server thread/INFO]: [ServiceIO] Registered placeholders for LuckPermsGroupController (GroupController)
[03:00:57] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: serviceio [3.0.0-pre1]
[03:00:57] [Server thread/INFO]: [LuckPerms] Registered Vault permission & chat hook.
[03:00:57] [Server thread/INFO]: [eco] Enabling eco v6.76.2
[03:00:57] [Server thread/INFO]: [eco] Loading eco
[03:00:57] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: eco [6.76.2]
[03:00:57] [Server thread/INFO]: [eco] Loaded integrations: PlaceholderAPI
[03:00:57] [Server thread/INFO]: [eco] Scanning for conflicts...
[03:00:57] [Server thread/INFO]: [eco] No conflicts found!
[03:00:57] [Server thread/INFO]: [EcoEnchants] Enabling EcoEnchants v12.23.1
[03:00:57] [Server thread/INFO]: [EcoEnchants] Loading EcoEnchants
[03:00:57] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoenchants [12.23.1]
[03:00:57] [Server thread/INFO]: Preparing level "world"
[03:00:58] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[03:00:58] [Server thread/INFO]: Preparing spawn area: 0%
[03:00:58] [Server thread/INFO]: Preparing spawn area: 75%
[03:00:59] [Server thread/INFO]: Time elapsed: 590 ms
[03:00:59] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[03:00:59] [Server thread/INFO]: Preparing spawn area: 0%
[03:00:59] [Server thread/INFO]: Time elapsed: 86 ms
[03:00:59] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[03:00:59] [Server thread/INFO]: Preparing spawn area: 0%
[03:00:59] [Server thread/INFO]: Time elapsed: 31 ms
[03:00:59] [Server thread/INFO]: [PlaceholderAPI] Enabling PlaceholderAPI v2.11.7-DEV-212
[03:00:59] [Server thread/INFO]: [PlaceholderAPI] Fetching available expansion information...
[03:00:59] [Server thread/INFO]: [EcoItems] Enabling EcoItems v5.63.1
[03:00:59] [Server thread/INFO]: [EcoItems] Loading EcoItems
[03:00:59] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoitems [5.63.1]
[03:00:59] [Server thread/INFO]: [AutoPluginLoader] Enabling AutoPluginLoader v1.5.1
[03:00:59] [Server thread/INFO]: [AutoPluginLoader] Loading libraries...
[03:00:59] [Server thread/INFO]: [AutoPluginLoader] Loaded successfully. (Took 8ms)
[03:00:59] [Server thread/INFO]: [EcoMobs] Enabling EcoMobs v10.21.1
[03:00:59] [Server thread/INFO]: [EcoMobs] Loading EcoMobs
[03:00:59] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecomobs [10.21.1]
[03:00:59] [Server thread/INFO]: [OpenJS] Enabling OpenJS v1.1.0
[03:00:59] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: openjs [1.1.0]
[03:00:59] [Server thread/INFO]: [OpenJS] [<------------------------------->]
[03:00:59] [Server thread/INFO]: [OpenJS]       [Loaded OpenJavascript]
[03:00:59] [Server thread/INFO]: [OpenJS] Version: 1.1.0
[03:00:59] [Server thread/INFO]: [OpenJS] Author: coolcostupit
[03:00:59] [Server thread/INFO]: [OpenJS] Java Version: 21.0.7
[03:00:59] [Server thread/INFO]: [OpenJS] Folia Support: true
[03:00:59] [Server thread/INFO]: [OpenJS] PlaceholderApi Support: true
[03:00:59] [Server thread/INFO]: [OpenJS] [<------------------------------->]
[03:00:59] [Server thread/INFO]: [EcoArmor] Enabling EcoArmor v8.75.1
[03:00:59] [Server thread/INFO]: [EcoArmor] Loading EcoArmor
[03:00:59] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoarmor [8.75.1]
[03:00:59] [Server thread/INFO]: [CustomCrops] Enabling CustomCrops v3.6.42
[03:00:59] [Server thread/WARN]: [CustomCrops] CraftEngine/ItemsAdder/Oraxen/Nexo/MythicCrucible are not installed. You can safely ignore this if you implemented the custom item interface with API.
[03:00:59] [Server thread/INFO]: [CustomCrops] Vault hooked!
[03:00:59] [Server thread/INFO]: [CustomCrops] PlaceholderAPI hooked!
[03:00:59] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: customcrops [3.6]
[03:00:59] [Server thread/WARN]: [CustomCrops] en_us.yml not exists, using en.yml as default locale.
[03:00:59] [Server thread/INFO]: [FreedomChat] Enabling FreedomChat v1.7.5
[03:00:59] [Server thread/INFO]: [RoseResourcepack] Enabling RoseResourcepack v3.3.4
[03:01:00] [customcrops-worker-3/WARN]: [CustomCrops] Update is available: https://github.com/Xiao-MoMi/Custom-Crops/
[03:01:00] [Server thread/INFO]: [Actions] Enabling Actions v2.74.1
[03:01:00] [Server thread/INFO]: [Actions] Loading Actions
[03:01:00] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: actions [2.74.1]
[03:01:00] [ForkJoinPool.commonPool-worker-4/WARN]: Duplicate entry found and skipped: pack.mcmeta
[03:01:00] [Server thread/INFO]: [ImageEmojis] Enabling ImageEmojis v1.5.2
[03:01:00] [ForkJoinPool.commonPool-worker-4/INFO]: Resource pack main successfully packed into archive
[03:01:00] [Server thread/INFO]: [ImageEmojis] Enforcement policy set to NONE: No need to start the resource pack HTTP server.
[03:01:00] [Server thread/INFO]: [libreforge] Enabling libreforge v4.76.1
[03:01:00] [Server thread/INFO]: [libreforge] Loading libreforge
[03:01:00] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: libreforge [4.76.1]
[03:01:00] [Server thread/INFO]: [libreforge] Loaded integrations: CustomCrops
[03:01:00] [Server thread/INFO]: [libreforge] 
[03:01:00] [Server thread/INFO]: [libreforge] Hey, what's this plugin doing here? I didn't install it!
[03:01:00] [Server thread/INFO]: [libreforge] libreforge is the effects system for plugins like EcoEnchants,
[03:01:00] [Server thread/INFO]: [libreforge] EcoJobs, EcoItems, etc. If you're looking for config options for
[03:01:00] [Server thread/INFO]: [libreforge] things like cooldown messages, lrcdb, and stuff like that, you'll
[03:01:00] [Server thread/INFO]: [libreforge] find it under /plugins/libreforge
[03:01:00] [Server thread/INFO]: [libreforge] 
[03:01:00] [Server thread/INFO]: [libreforge] Don't worry about updating libreforge, it's handled automatically!
[03:01:00] [Server thread/INFO]: [libreforge] 
[03:01:00] [Server thread/INFO]: [spark] Starting background profiler...
[03:01:00] [Server thread/INFO]: [PlaceholderAPI] Placeholder expansion registration initializing...
[03:01:00] [Server thread/INFO]: 0 placeholder hook(s) registered!
[03:01:00] [Server thread/INFO]: Done preparing level "world" (3.520s)
[03:01:01] [Server thread/INFO]: Running delayed init tasks
[03:01:01] [Server thread/INFO]: [CustomCrops] Registry access has been frozen
[03:01:01] [Server thread/INFO]: Check for updates...
[03:01:01] [Server thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[03:01:01] [Server thread/INFO]: Done (20.270s)! For help, type "help"
[03:01:01] [Server thread/INFO]: [eco] Loaded eco
[03:01:01] [Server thread/WARN]: [EcoEnchants] 19 enchantments were not loaded because they need EcoSkills to be installed!
[03:01:01] [Server thread/WARN]: [EcoEnchants] Either download EcoSkills or delete the folder at /plugins/EcoEnchants/enchants/ecoskills to remove this message
[03:01:01] [Server thread/WARN]: [EcoEnchants] 1 enchantments were not loaded because they need EcoJobs to be installed!
[03:01:01] [Server thread/WARN]: [EcoEnchants] Either download EcoJobs or delete the folder at /plugins/EcoEnchants/enchants/ecojobs to remove this message
[03:01:01] [Server thread/WARN]: [EcoEnchants] 1 enchantments were not loaded because they need EcoPets to be installed!
[03:01:01] [Server thread/WARN]: [EcoEnchants] Either download EcoPets or delete the folder at /plugins/EcoEnchants/enchants/ecopets to remove this message
[03:01:01] [Server thread/INFO]: [EcoEnchants] Loaded EcoEnchants
[03:01:01] [Server thread/INFO]: [EcoItems] Loaded EcoItems
[03:01:01] [Server thread/INFO]: [EcoMobs] Loaded EcoMobs
[03:01:01] [Server thread/INFO]: [EcoArmor] Loaded EcoArmor
[03:01:01] [Server thread/INFO]: [Actions] Loaded Actions
[03:01:01] [Server thread/INFO]: [libreforge] Loaded libreforge
[04:01:00] [Server thread/INFO]: Check for updates...
[05:01:00] [Server thread/INFO]: Check for updates...
[06:01:00] [Server thread/INFO]: Check for updates...
[07:01:00] [Server thread/INFO]: Check for updates...
[07:51:03] [pool-20-thread-1/INFO]: [org.apache.http.impl.execchain.RetryExec] I/O exception (java.net.SocketException) caught when processing request to {s}->https://api.spigotmc.org:443: Network is unreachable
[07:51:03] [pool-20-thread-1/INFO]: [org.apache.http.impl.execchain.RetryExec] Retrying request to {s}->https://api.spigotmc.org:443
[07:51:10] [pool-20-thread-1/INFO]: [org.apache.http.impl.execchain.RetryExec] I/O exception (java.net.SocketException) caught when processing request to {s}->https://api.spigotmc.org:443: Network is unreachable
[07:51:10] [pool-20-thread-1/INFO]: [org.apache.http.impl.execchain.RetryExec] Retrying request to {s}->https://api.spigotmc.org:443
[08:01:00] [Server thread/INFO]: Check for updates...
[09:01:00] [Server thread/INFO]: Check for updates...
[10:01:00] [Server thread/INFO]: Check for updates...
