[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m | ------------------------------------------- |[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m      ___       __       ___  __             [m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m     / _ |__ __/ /____  / _ \/ /_ _____ _   [m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m    / __ / // / __/ _ \/ ___/ / // / _ `/   [m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m   /_/ |_\_,_/\__/\___/_/  /_/\_,_/\_, /[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m                                  /___/    [m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m AutoPlug-Client 8.3.1 by Osiris-Team[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m Web-Panel: https://autoplug.one/[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m | ------------------------------------------- |[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m Checked configs, took 166ms[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m Authenticating server...[m
[47;30m[19-07-2025 16:42:37][36m[AP][33m[WARN][m[33m No valid key provided. Register your server at https://autoplug.one/, get your server-key and add it to the /autoplug/general.yml config file. Enter '.con reload' to retry.[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m Initialised successfully.[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m | ------------------------------------------- |[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m Enter .help for a list of all commands.[m
[47;30m[19-07-2025 16:42:37][36m[AP][30m[INFO][m Running pre-startup tasks, please be patient...[m
[47;30m[19-07-2025 16:42:38][36m[AP][30m[INFO][m [TASK] [\] > [SelfUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:38][36m[AP][30m[INFO][m [m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version![m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/CustomCrops/libs/zstd-jni-1.5.6-9.jar 0% - ADD_ENTRY[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [/] > [GeneralTasks] Initialising...[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [-] > [DailyRestarter] Initialising...[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [\] > [CustomRestarter] Initialising...[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [|] > [JavaUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [/] > [ServerUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [-] > [PluginsUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [TASK] [\] > [ModsUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:43][36m[AP][30m[INFO][m [m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version![m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/server.jar 0% - ADD_ENTRY[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [/] > [GeneralTasks] Initialising...[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [-] > [DailyRestarter] Initialising...[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [\] > [CustomRestarter] Initialising...[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [|] > [JavaUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [/] > [ServerUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [-] > [PluginsUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [TASK] [\] > [ModsUpdater] Initialising...[m
[47;30m[19-07-2025 16:42:48][36m[AP][30m[INFO][m [m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version![m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [#] > [GeneralTasks][100%] Finished.[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [|] > [DailyRestarter] Initialising...[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [#] > [CustomRestarter] Skipped.[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [/] > [JavaUpdater] Update found (6 -> 9), started download![m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [#] > [ServerUpdater][100%] Up-to-date version[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [-] > [PluginsUpdater] Checked 'FreedomChat' plugin (2/16)[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [#] > [ModsUpdater] Skipped.[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [TASK] [\] > [JavaDownloader] Downloading JDK-21.0.8+9.0.LTS.file... (0mb/0mb)[m
[47;30m[19-07-2025 16:42:53][36m[AP][30m[INFO][m [m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version![m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [GeneralTasks][100%] Finished.[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0][m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [CustomRestarter] Skipped.[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [|] > [JavaUpdater] Java update downloaded. Removing old installation...[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [ServerUpdater][100%] Up-to-date version[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [/] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (14/16)[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [ModsUpdater] Skipped.[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)[m
[47;30m[19-07-2025 16:42:58][36m[AP][30m[INFO][m [m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version![m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [GeneralTasks][100%] Finished.[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0][m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [CustomRestarter] Skipped.[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [JavaUpdater][100%] Java update was installed successfully (6 -> 9)![m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [ServerUpdater][100%] Up-to-date version[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [ModsUpdater] Skipped.[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m [m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m GeneralTasks:[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m Directory cleaner removed 1 files, from directory ./autoplug/logs[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m PluginsUpdater:[m
[47;30m[19-07-2025 16:43:02][36m[AP][33m[WARN][m[33m There was an api-error for RoseResourcepack! Details: Index 0 out of bounds for length 0[m
[47;30m[19-07-2025 16:43:02][36m[AP][33m[WARN][m[33m There was an api-error for OpenJS! Details: Index 0 out of bounds for length 0[m
[47;30m[19-07-2025 16:43:02][36m[AP][33m[WARN][m[33m Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.[m
[47;30m[19-07-2025 16:43:02][36m[AP][33m[WARN][m[33m Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.[m
[47;30m[19-07-2025 16:43:02][36m[AP][33m[WARN][m[33m Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.[m
[47;30m[19-07-2025 16:43:02][36m[AP][30m[INFO][m Starting server: server.jar[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version![m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [GeneralTasks][100%] Finished.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0][m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [CustomRestarter] Skipped.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [JavaUpdater][100%] Java update was installed successfully (6 -> 9)![m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [ServerUpdater][100%] Up-to-date version[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [ModsUpdater] Skipped.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version![m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [GeneralTasks][100%] Finished.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0][m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [CustomRestarter] Skipped.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [JavaUpdater][100%] Java update was installed successfully (6 -> 9)![m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [ServerUpdater][100%] Up-to-date version[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [ModsUpdater] Skipped.[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)[m
[47;30m[19-07-2025 16:43:03][36m[AP][30m[INFO][m [m
Starting org.bukkit.craftbukkit.Main[m
[33m2025-07-19T20:43:04.361325385Z ServerMain WARN Advanced terminal features are not available in this environment[m
[16:43:04 INFO]: [bootstrap] Running Java 21 (OpenJDK 64-Bit Server VM 21.0.7+6-LTS; Eclipse Adoptium Temurin-21.0.7+6) on Linux 6.15.6-zen1-1-zen (amd64)[m
[16:43:04 INFO]: [bootstrap] Loading Purpur 1.21.7-2477-HEAD@60bdf1c (2025-07-18T03:06:59Z) for Minecraft 1.21.7[m
[16:43:04 INFO]: [PluginInitializerManager] Initializing plugins...[m
[16:43:05 INFO]: [PluginInitializerManager] Initialized 16 plugins[m
[16:43:05 INFO]: [PluginInitializerManager] Paper plugins (1):[m
 - ServiceIO (3.0.0-pre1)[m
[16:43:05 INFO]: [PluginInitializerManager] Bukkit plugins (15):[m
 - Actions (2.74.1), AutoPluginLoader (1.5.1), CustomCrops (3.6.42), EcoArmor (8.75.1), EcoEnchants (12.23.1), EcoItems (5.63.1), EcoMobs (10.21.1), FreedomChat (1.7.5), ImageEmojis (1.5.2), LuckPerms (5.5.9), OpenJS (1.1.0), PlaceholderAPI (2.11.7-DEV-212), ProtocolLib (5.4.0-SNAPSHOT-753), RoseResourcepack (3.3.4), eco (6.76.2)[m
[16:43:09 INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD][m
[16:43:10 INFO]: Loaded 1407 recipes[m
[16:43:10 INFO]: Loaded 1520 advancements[m
[16:43:10 INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Initialising converters for DataConverter...[m
[16:43:10 INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Finished initialising converters for DataConverter in 221.7ms[m
[16:43:10 INFO]: Starting minecraft server version 1.21.7[m
[16:43:10 INFO]: Loading properties[m
[16:43:10 INFO]: This server is running Purpur version 1.21.7-2477-HEAD@60bdf1c (2025-07-18T03:06:59Z) (Implementing API version 1.21.7-R0.1-SNAPSHOT)[m
[16:43:10 INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling[m
[16:43:10 INFO]: Server Ping Player Sample Count: 12[m
[16:43:10 INFO]: Using 4 threads for Netty based IO[m
[16:43:11 INFO]: [MoonriseCommon] Paper is using 3 worker threads, 1 I/O threads[m
[16:43:11 INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true[m
[16:43:11 INFO]: Default game type: SURVIVAL[m
[16:43:11 INFO]: Generating keypair[m
[16:43:11 INFO]: Starting Minecraft server on *:25565[m
[16:43:11 INFO]: Using epoll channel type[m
[16:43:11 INFO]: Paper: Using libdeflate (Linux x86_64) compression from Velocity.[m
[16:43:11 INFO]: Paper: Using OpenSSL 3.x.x (Linux x86_64) cipher from Velocity.[m
[16:43:11 INFO]: [eco] Initializing [38;2;85;255;85meco[0m[m
[16:43:12 INFO]: [EcoItems] Initializing [38;2;255;0;0mEcoItems[0m[m
[16:43:15 INFO]: [EcoMobs] Initializing [38;2;85;85;255mEcoMobs[0m[m
[16:43:22 INFO]: [EcoEnchants] Initializing [38;2;85;255;85mEcoEnchants[0m[m
[16:43:22 INFO]: [EcoArmor] Initializing [38;2;255;85;85mEcoArmor[0m[m
[16:43:24 INFO]: [Actions] Initializing [38;2;45;45;45mActions[0m[m
[16:43:25 INFO]: [LuckPerms] Loading server plugin LuckPerms v5.5.9[m
[16:43:26 INFO]: [LuckPerms] Loading configuration...[m
[16:43:26 INFO]: [ProtocolLib] Loading server plugin ProtocolLib v5.4.0-SNAPSHOT-753[m
[33m[16:43:26 WARN]: [ProtocolLib] Version (MC: 1.21.7) has not yet been tested! Proceed with caution.[m
[16:43:26 INFO]: [PlaceholderAPI] Loading server plugin PlaceholderAPI v2.11.7-DEV-212[m
[16:43:26 INFO]: [ServiceIO] Loading server plugin ServiceIO v3.0.0-pre1[m
[16:43:26 INFO]: [eco] Loading server plugin eco v6.76.2[m
[16:43:26 INFO]: [EcoItems] Loading server plugin EcoItems v5.63.1[m
[16:43:26 INFO]: [libreforge] Initializing [38;2;180;255;51mlibreforge[0m[m
[16:43:26 INFO]: [libreforge] Loading server plugin libreforge v4.76.1[m
[16:43:26 INFO]: [AutoPluginLoader] Loading server plugin AutoPluginLoader v1.5.1[m
[16:43:26 INFO]: [EcoMobs] Loading server plugin EcoMobs v10.21.1[m
[16:43:26 INFO]: [OpenJS] Loading server plugin OpenJS v1.1.0[m
[16:43:26 INFO]: [EcoEnchants] Loading server plugin EcoEnchants v12.23.1[m
[16:43:26 INFO]: [ServiceIO] You are running the latest version of ServiceIO[m
[16:43:27 INFO]: [EcoArmor] Loading server plugin EcoArmor v8.75.1[m
[16:43:27 INFO]: [CustomCrops] Loading server plugin CustomCrops v3.6.42[m
[16:43:27 INFO]: [FreedomChat] Loading server plugin FreedomChat v1.7.5[m
[16:43:27 INFO]: [RoseResourcepack] Loading server plugin RoseResourcepack v3.3.4[m
[16:43:27 INFO]: [Actions] Loading server plugin Actions v2.74.1[m
[16:43:27 INFO]: [ImageEmojis] Loading server plugin ImageEmojis v1.5.2[m
[16:43:27 INFO]: Server permissions file permissions.yml is empty, ignoring it[m
[16:43:27 INFO]: [LuckPerms] Enabling LuckPerms v5.5.9[m
[16:43:27 INFO]: [38;2;85;255;255m       [38;2;0;170;170m __    [0m[m
[16:43:27 INFO]: [38;2;85;255;255m  |    [38;2;0;170;170m|__)   [38;2;0;170;0mLuckPerms[0m [38;2;85;255;255mv5.5.9[0m[m
[16:43:27 INFO]: [38;2;85;255;255m  |___ [38;2;0;170;170m|      [38;2;85;85;85mRunning on Bukkit - Purpur[0m[m
[16:43:27 INFO]: [m
[16:43:27 INFO]: [LuckPerms] Loading storage provider... [H2][m
[16:43:28 INFO]: [LuckPerms] Loading internal permission managers...[m
[16:43:28 INFO]: [LuckPerms] Performing initial data load...[m
[16:43:28 INFO]: [LuckPerms] Successfully enabled. (took 1023ms)[m
[16:43:28 INFO]: [ProtocolLib] Enabling ProtocolLib v5.4.0-SNAPSHOT-753[m
[16:43:28 INFO]: [ServiceIO] Enabling ServiceIO v3.0.0-pre1[m
[16:43:28 INFO]: [ServiceIO] Initialized support for LuckPerms as PermissionController (Highest)[m
[16:43:28 INFO]: [ServiceIO] Initialized support for LuckPerms Groups as GroupController (Highest)[m
[16:43:28 INFO]: [ServiceIO] Initialized support for LuckPerms Chat as ChatController (Highest)[m
[16:43:28 INFO]: [ServiceIO] Registered placeholders for LuckPermsChatController (ChatController)[m
[16:43:28 INFO]: [ServiceIO] Registered placeholders for LuckPermsGroupController (GroupController)[m
[16:43:28 INFO]: [PlaceholderAPI] Successfully registered internal expansion: serviceio [3.0.0-pre1][m
[16:43:28 INFO]: [LuckPerms] Registered Vault permission & chat hook.[m
[16:43:28 INFO]: [eco] Enabling eco v6.76.2[m
[16:43:28 INFO]: [eco] Loading [38;2;85;255;85meco[0m[m
[16:43:28 INFO]: [PlaceholderAPI] Successfully registered internal expansion: eco [6.76.2][m
[16:43:28 INFO]: [eco] Loaded integrations: PlaceholderAPI[m
[16:43:28 INFO]: [eco] Scanning for conflicts...[m
[16:43:28 INFO]: [eco] No conflicts found![m
[16:43:28 INFO]: [EcoEnchants] Enabling EcoEnchants v12.23.1[m
[16:43:28 INFO]: [EcoEnchants] Loading [38;2;85;255;85mEcoEnchants[0m[m
[16:43:28 INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoenchants [12.23.1][m
[16:43:28 INFO]: Preparing level "world"[m
[16:43:29 INFO]: Preparing start region for dimension minecraft:overworld[m
[16:43:29 INFO]: Preparing spawn area: 0%[m
[16:43:30 INFO]: Preparing spawn area: 40%[m
[16:43:30 INFO]: Time elapsed: 673 ms[m
[16:43:30 INFO]: Preparing start region for dimension minecraft:the_nether[m
[16:43:30 INFO]: Preparing spawn area: 0%[m
[16:43:30 INFO]: Time elapsed: 88 ms[m
[16:43:30 INFO]: Preparing start region for dimension minecraft:the_end[m
[16:43:30 INFO]: Preparing spawn area: 0%[m
[16:43:30 INFO]: Time elapsed: 65 ms[m
[16:43:30 INFO]: [PlaceholderAPI] Enabling PlaceholderAPI v2.11.7-DEV-212[m
[16:43:30 INFO]: [PlaceholderAPI] Fetching available expansion information...[m
[16:43:30 INFO]: [EcoItems] Enabling EcoItems v5.63.1[m
[16:43:30 INFO]: [EcoItems] Loading [38;2;255;0;0mEcoItems[0m[m
[16:43:30 INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoitems [5.63.1][m
[16:43:30 INFO]: [AutoPluginLoader] Enabling AutoPluginLoader v1.5.1[m
[16:43:30 INFO]: [AutoPluginLoader] Loading libraries...[m
[16:43:30 INFO]: [AutoPluginLoader] Loaded successfully. (Took 58ms)[m
[16:43:30 INFO]: [EcoMobs] Enabling EcoMobs v10.21.1[m
[16:43:30 INFO]: [EcoMobs] Loading [38;2;85;85;255mEcoMobs[0m[m
[16:43:30 INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecomobs [10.21.1][m
[16:43:30 INFO]: [OpenJS] Enabling OpenJS v1.1.0[m
[16:43:30 INFO]: [PlaceholderAPI] Successfully registered internal expansion: openjs [1.1.0][m
[16:43:30 INFO]: [OpenJS] [34m[<------------------------------->][0m[m
[16:43:30 INFO]: [OpenJS] [38;5;81m      [Loaded OpenJavascript][0m[m
[16:43:30 INFO]: [OpenJS] [38;5;81mVersion: 1.1.0[0m[m
[16:43:30 INFO]: [OpenJS] [38;5;81mAuthor: coolcostupit[0m[m
[16:43:30 INFO]: [OpenJS] [38;5;81mJava Version: 21.0.7[0m[m
[16:43:30 INFO]: [OpenJS] [38;5;81mFolia Support: true[0m[m
[16:43:30 INFO]: [OpenJS] [38;5;81mPlaceholderApi Support: true[0m[m
[16:43:30 INFO]: [OpenJS] [34m[<------------------------------->][0m[m
[16:43:30 INFO]: [EcoArmor] Enabling EcoArmor v8.75.1[m
[16:43:30 INFO]: [EcoArmor] Loading [38;2;255;85;85mEcoArmor[0m[m
[16:43:30 INFO]: [PlaceholderAPI] Successfully registered internal expansion: ecoarmor [8.75.1][m
[16:43:30 INFO]: [CustomCrops] Enabling CustomCrops v3.6.42[m
[33m[16:43:31 WARN]: [CustomCrops] CraftEngine/ItemsAdder/Oraxen/Nexo/MythicCrucible are not installed. You can safely ignore this if you implemented the custom item interface with API.[m
[16:43:31 INFO]: [CustomCrops] Vault hooked![m
[16:43:31 INFO]: [CustomCrops] PlaceholderAPI hooked![m
[16:43:31 INFO]: [PlaceholderAPI] Successfully registered internal expansion: customcrops [3.6][m
[33m[16:43:31 WARN]: [CustomCrops] en_us.yml not exists, using en.yml as default locale.[m
[16:43:31 INFO]: [FreedomChat] Enabling FreedomChat v1.7.5[m
[16:43:31 INFO]: [RoseResourcepack] Enabling RoseResourcepack v3.3.4[m
[33m[16:43:31 WARN]: [CustomCrops] Update is available: https://github.com/Xiao-MoMi/Custom-Crops/[m
[16:43:31 INFO]: [Actions] Enabling Actions v2.74.1[m
[16:43:31 INFO]: [Actions] Loading [38;2;45;45;45mActions[0m[m
[16:43:31 INFO]: [PlaceholderAPI] Successfully registered internal expansion: actions [2.74.1][m
[33m[16:43:31 WARN]: Duplicate entry found and skipped: pack.mcmeta[m
[16:43:31 INFO]: [ImageEmojis] Enabling ImageEmojis v1.5.2[m
[16:43:31 INFO]: [38;2;85;255;85mResource pack main successfully packed into archive[0m[m
[16:43:31 INFO]: [ImageEmojis] Enforcement policy set to NONE: No need to start the resource pack HTTP server.[m
[16:43:31 INFO]: [libreforge] Enabling libreforge v4.76.1[m
[16:43:31 INFO]: [libreforge] Loading [38;2;180;255;51mlibreforge[0m[m
[16:43:31 INFO]: [PlaceholderAPI] Successfully registered internal expansion: libreforge [4.76.1][m
[16:43:31 INFO]: [libreforge] Loaded integrations: CustomCrops[m
[16:43:31 INFO]: [libreforge] [m
[16:43:31 INFO]: [libreforge] Hey, what's this plugin doing here? I didn't install it![m
[16:43:31 INFO]: [libreforge] libreforge is the effects system for plugins like EcoEnchants,[m
[16:43:31 INFO]: [libreforge] EcoJobs, EcoItems, etc. If you're looking for config options for[m
[16:43:31 INFO]: [libreforge] things like cooldown messages, lrcdb, and stuff like that, you'll[m
[16:43:31 INFO]: [libreforge] find it under /plugins/libreforge[m
[16:43:31 INFO]: [libreforge] [m
[16:43:31 INFO]: [libreforge] Don't worry about updating libreforge, it's handled automatically![m
[16:43:31 INFO]: [libreforge] [m
[16:43:32 INFO]: [spark] Starting background profiler...[m
[16:43:32 INFO]: [PlaceholderAPI] Placeholder expansion registration initializing...[m
[16:43:32 INFO]: [38;2;255;170;0m0 placeholder hook(s) registered![0m[m
[16:43:32 INFO]: Done preparing level "world" (3.477s)[m
[16:43:32 INFO]: Running delayed init tasks[m
[16:43:32 INFO]: [CustomCrops] Registry access has been frozen[m
[16:43:32 INFO]: Check for updates...[m
[16:43:32 INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD][m
[16:43:32 INFO]: Done (28.918s)! For help, type "help"[m
[16:43:32 INFO]: [eco] Loaded [38;2;85;255;85meco[0m[m
[33m[16:43:33 WARN]: [EcoEnchants] 19 enchantments were not loaded because they need EcoSkills to be installed![m
[33m[16:43:33 WARN]: [EcoEnchants] Either download EcoSkills or delete the folder at /plugins/EcoEnchants/enchants/ecoskills to remove this message[m
[33m[16:43:33 WARN]: [EcoEnchants] 1 enchantments were not loaded because they need EcoJobs to be installed![m
[33m[16:43:33 WARN]: [EcoEnchants] Either download EcoJobs or delete the folder at /plugins/EcoEnchants/enchants/ecojobs to remove this message[m
[33m[16:43:33 WARN]: [EcoEnchants] 1 enchantments were not loaded because they need EcoPets to be installed![m
[33m[16:43:33 WARN]: [EcoEnchants] Either download EcoPets or delete the folder at /plugins/EcoEnchants/enchants/ecopets to remove this message[m
[16:43:33 INFO]: [EcoEnchants] Loaded [38;2;85;255;85mEcoEnchants[0m[m
[16:43:33 INFO]: [EcoItems] Loaded [38;2;255;0;0mEcoItems[0m[m
[16:43:33 INFO]: [EcoMobs] Loaded [38;2;85;85;255mEcoMobs[0m[m
[16:43:33 INFO]: [EcoArmor] Loaded [38;2;255;85;85mEcoArmor[0m[m
[16:43:33 INFO]: [Actions] Loaded [38;2;45;45;45mActions[0m[m
[16:43:33 INFO]: [libreforge] Loaded [38;2;180;255;51mlibreforge[0m[m
[16:45:24 INFO]: UUID of player Cunyarc is 66e9f0fe-0991-4fef-b0c2-5bd0354aa36f[m
[16:45:24 INFO]: [HorriblePlayerLoginEventHack] You have plugins listening to the PlayerLoginEvent, this will cause re-configuration APIs to be unavailable: [eco, LuckPerms, CustomCrops, ProtocolLib][m
[16:45:25 INFO]: [38;2;255;255;85mCunyarc joined the game[0m[m
[16:45:25 INFO]: Cunyarc[/127.0.0.1:32914] logged in with entity id 15 at ([world]-1106.3690342532504, 83.0, -789.5195553526054)[m
[16:45:35 INFO]: Cunyarc issued server command: /time set day[m
[16:45:35 INFO]: [3m[38;2;170;170;170m[Cunyarc: Set the time to 1000][0m[m
[16:45:43 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[16:48:35 INFO]: Cunyarc issued server command: /ecoitems reload[m
[16:48:44 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[16:49:59 INFO]: Cunyarc issued server command: /ecoitems reload[m
[16:50:10 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[16:57:13 INFO]: Cunyarc issued server command: /ecoitems reload[m
[16:57:19 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[16:57:38 INFO]: Cunyarc issued server command: /ecoitems reload[m
[16:57:44 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[16:58:27 INFO]: Cunyarc issued server command: /time set day[m
[16:58:27 INFO]: [3m[38;2;170;170;170m[Cunyarc: Set the time to 1000][0m[m
[16:58:36 INFO]: Cunyarc issued server command: /kill @e[type=!player][m
[16:58:36 INFO]: [3m[38;2;170;170;170m[Cunyarc: Killed 223 entities][0m[m
[17:08:15 INFO]: Cunyarc issued server command: /time set day[m
[17:08:15 INFO]: [3m[38;2;170;170;170m[Cunyarc: Set the time to 1000][0m[m
[17:08:18 INFO]: Cunyarc issued server command: /ecoitems reload[m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] Invalid configuration for translate_location found at Item ID hammer_wooden -> mutators -> args:[m
[31m[17:08:18 ERROR]: [EcoItems] (Cause) Argument 'add_x'[m
[31m[17:08:18 ERROR]: [EcoItems] (Reason) You must specify the value to add to x![m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] Invalid configuration for translate_location found at Item ID hammer_wooden -> mutators -> args:[m
[31m[17:08:18 ERROR]: [EcoItems] (Cause) Argument 'add_y'[m
[31m[17:08:18 ERROR]: [EcoItems] (Reason) You must specify the value to add to y![m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] Invalid configuration for translate_location found at Item ID hammer_wooden -> mutators -> args:[m
[31m[17:08:18 ERROR]: [EcoItems] (Cause) Argument 'add_z'[m
[31m[17:08:18 ERROR]: [EcoItems] (Reason) You must specify the value to add to z![m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] Invalid configuration for translate_location found at Item ID hammer_wooden -> mutators -> args:[m
[31m[17:08:18 ERROR]: [EcoItems] (Cause) Argument 'add_x'[m
[31m[17:08:18 ERROR]: [EcoItems] (Reason) You must specify the value to add to x![m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] Invalid configuration for translate_location found at Item ID hammer_wooden -> mutators -> args:[m
[31m[17:08:18 ERROR]: [EcoItems] (Cause) Argument 'add_y'[m
[31m[17:08:18 ERROR]: [EcoItems] (Reason) You must specify the value to add to y![m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] [m
[31m[17:08:18 ERROR]: [EcoItems] Invalid configuration for translate_location found at Item ID hammer_wooden -> mutators -> args:[m
[31m[17:08:18 ERROR]: [EcoItems] (Cause) Argument 'add_z'[m
[31m[17:08:18 ERROR]: [EcoItems] (Reason) You must specify the value to add to z![m
[31m[17:08:18 ERROR]: [EcoItems] [m
[17:09:21 INFO]: Cunyarc issued server command: /ecoitems reload[m
[17:09:28 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[17:09:55 INFO]: Cunyarc issued server command: /ecoitems reload[m
[17:09:57 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[17:12:16 INFO]: Cunyarc issued server command: /ecoitems reload[m
[17:12:20 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[17:12:43 INFO]: Cunyarc issued server command: /ecoitems reload[m
[17:12:44 INFO]: Cunyarc issued server command: /ecoitems give Cunyarc hammer_wooden[m
[17:43:32 INFO]: Check for updates...[m
[18:43:32 INFO]: Check for updates...[m
[19:43:32 INFO]: Check for updates...[m
[20:36:20 INFO]: Cunyarc lost connection: Disconnected[m
[20:36:20 INFO]: [38;2;255;255;85mCunyarc left the game[0m[m
[47;30m[19-07-2025 20:36:34][36m[AP][30m[INFO][m Stopping server...[m
[20:36:34 INFO]: Stopping the server[m
[20:36:34 INFO]: Stopping server[m
[20:36:34 INFO]: [libreforge] Disabling libreforge v4.76.1[m
[20:36:34 INFO]: [libreforge] Cleaning up...[m
[20:36:34 INFO]: [ImageEmojis] Disabling ImageEmojis v1.5.2[m
[20:36:34 INFO]: [Actions] Disabling Actions v2.74.1[m
[20:36:34 INFO]: [Actions] Cleaning up...[m
[20:36:34 INFO]: [RoseResourcepack] Disabling RoseResourcepack v3.3.4[m
[20:36:34 INFO]: [FreedomChat] Disabling FreedomChat v1.7.5[m
[20:36:34 INFO]: [CustomCrops] Disabling CustomCrops v3.6.42[m
[31m[20:36:34 WARN]: java.net.SocketException: Socket closed[m
[33m[20:36:34 WARN]: 	at java.base/sun.nio.ch.NioSocketImpl.endAccept(NioSocketImpl.java:682)[m
[33m[20:36:34 WARN]: 	at java.base/sun.nio.ch.NioSocketImpl.accept(NioSocketImpl.java:755)[m
[33m[20:36:34 WARN]: 	at java.base/java.net.ServerSocket.implAccept(ServerSocket.java:698)[m
[33m[20:36:34 WARN]: 	at java.base/java.net.ServerSocket.platformImplAccept(ServerSocket.java:663)[m
[33m[20:36:34 WARN]: 	at java.base/java.net.ServerSocket.implAccept(ServerSocket.java:639)[m
[33m[20:36:34 WARN]: 	at java.base/java.net.ServerSocket.implAccept(ServerSocket.java:585)[m
[33m[20:36:34 WARN]: 	at java.base/java.net.ServerSocket.accept(ServerSocket.java:543)[m
[33m[20:36:34 WARN]: 	at RoseResourcepack-3.3.4.jar//me.emsockz.roserp.host.Hosting.lambda$handleRequest$0(Hosting.java:36)[m
[33m[20:36:34 WARN]: 	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)[m
[33m[20:36:34 WARN]: 	at org.bukkit.craftbukkit.scheduler.CraftAsyncTask.run(CraftAsyncTask.java:57)[m
[33m[20:36:34 WARN]: 	at com.destroystokyo.paper.ServerSchedulerReportingWrapper.run(ServerSchedulerReportingWrapper.java:22)[m
[33m[20:36:34 WARN]: 	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)[m
[33m[20:36:34 WARN]: 	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)[m
[33m[20:36:34 WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1583)[m
[20:36:34 INFO]: [EcoArmor] Disabling EcoArmor v8.75.1[m
[20:36:34 INFO]: [EcoArmor] Cleaning up...[m
[20:36:34 INFO]: [EcoEnchants] Disabling EcoEnchants v12.23.1[m
[20:36:34 INFO]: [EcoEnchants] Cleaning up...[m
[20:36:34 INFO]: [OpenJS] Disabling OpenJS v1.1.0[m
[20:36:34 INFO]: [OpenJS] [34m[<---------------------------------->][0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81m      [OpenJavascript shutdown][0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81mUn-registering all listeners...[0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81mUn-registering all tasks...[0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81mUn-registering all script commands...[0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81mUn-loading all scripts...[0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81mStoring memory variables...[0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81mSaving disk storage files...[0m[m
[20:36:34 INFO]: [OpenJS] [38;5;81m[OpenJavascript shutdown successfully][0m[m
[20:36:34 INFO]: [OpenJS] [34m[<---------------------------------->][0m[m
[20:36:34 INFO]: [EcoMobs] Disabling EcoMobs v10.21.1[m
[20:36:34 INFO]: [EcoMobs] Cleaning up...[m
[20:36:34 INFO]: [AutoPluginLoader] Disabling AutoPluginLoader v1.5.1[m
[20:36:34 INFO]: [AutoPluginLoader] Plugin has been disabled.[m
[20:36:34 INFO]: [EcoItems] Disabling EcoItems v5.63.1[m
[20:36:34 INFO]: [EcoItems] Cleaning up...[m
[20:36:34 INFO]: [eco] Disabling eco v6.76.2[m
[20:36:34 INFO]: [eco] Saving player data...[m
[20:36:35 INFO]: [eco] Saved player data! Took 4ms[m
[20:36:35 INFO]: [eco] Cleaning up...[m
[20:36:35 INFO]: [ServiceIO] Disabling ServiceIO v3.0.0-pre1[m
[20:36:35 INFO]: [PlaceholderAPI] Disabling PlaceholderAPI v2.11.7-DEV-212[m
[20:36:35 INFO]: [ProtocolLib] Disabling ProtocolLib v5.4.0-SNAPSHOT-753[m
[20:36:35 INFO]: [LuckPerms] Disabling LuckPerms v5.5.9[m
[20:36:35 INFO]: [LuckPerms] Starting shutdown process...[m
[20:36:35 INFO]: [LuckPerms] Closing storage...[m
[20:36:35 INFO]: [LuckPerms] Goodbye![m
[20:36:35 INFO]: Saving players[m
[20:36:35 INFO]: Saving worlds[m
[20:36:35 INFO]: Saving chunks for level 'ServerLevel[world]'/minecraft:overworld[m
[20:36:35 INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'world'[m
[20:36:35 INFO]: [ChunkHolderManager] Halted chunk system for world 'world'[m
[20:36:35 INFO]: [ChunkHolderManager] Saving all chunkholders for world 'world'[m
[20:36:35 INFO]: [ChunkHolderManager] Saved 0 block chunks, 3 entity chunks, 0 poi chunks in world 'world' in 0.12s[m
[20:36:35 INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'world'[m
[20:36:35 INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'world'[m
[20:36:35 INFO]: Saving chunks for level 'ServerLevel[world_nether]'/minecraft:the_nether[m
[20:36:36 INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'world_nether'[m
[20:36:36 INFO]: [ChunkHolderManager] Halted chunk system for world 'world_nether'[m
[20:36:36 INFO]: [ChunkHolderManager] Saving all chunkholders for world 'world_nether'[m
[20:36:36 INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'world_nether' in 0.01s[m
[20:36:36 INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'world_nether'[m
[20:36:36 INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'world_nether'[m
[20:36:36 INFO]: Saving chunks for level 'ServerLevel[world_the_end]'/minecraft:the_end[m
[20:36:36 INFO]: [ChunkHolderManager] Waiting 60s for chunk system to halt for world 'world_the_end'[m
[20:36:36 INFO]: [ChunkHolderManager] Halted chunk system for world 'world_the_end'[m
[20:36:36 INFO]: [ChunkHolderManager] Saving all chunkholders for world 'world_the_end'[m
[20:36:36 INFO]: [ChunkHolderManager] Saved 0 block chunks, 0 entity chunks, 0 poi chunks in world 'world_the_end' in 0.01s[m
[20:36:36 INFO]: [ChunkHolderManager] Waiting 60s for chunk I/O to halt for world 'world_the_end'[m
[20:36:36 INFO]: [ChunkHolderManager] Halted I/O scheduler for world 'world_the_end'[m
[20:36:36 INFO]: ThreadedAnvilChunkStorage (world): All chunks are saved[m
[20:36:36 INFO]: ThreadedAnvilChunkStorage (DIM-1): All chunks are saved[m
[20:36:36 INFO]: ThreadedAnvilChunkStorage (DIM1): All chunks are saved[m
[20:36:36 INFO]: ThreadedAnvilChunkStorage: All dimensions are saved[m
[20:36:36 INFO]: Waiting for all RegionFile I/O tasks to complete...[m
[20:36:36 INFO]: All RegionFile I/O tasks to complete[m
[20:36:36 INFO]: [MoonriseCommon] Awaiting termination of worker pool for up to 60s...[m
[20:36:36 INFO]: [MoonriseCommon] Awaiting termination of I/O pool for up to 60s...[m
[47;30m[19-07-2025 20:36:37][36m[AP][30m[INFO][m See you soon![m
