# Material list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html
# EntityType list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/EntityType.html
# Attribute list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/attribute/Attribute.html
# Potion effects list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/potion/PotionEffectType.html
# Random UUID generator: https://www.uuidgenerator.net/
# Color codes: https://docs.adventure.kyori.net/minimessage.html#format


caveblock:
  itemname: <#D5D6D8>Cave Block
  material: PAPER
  Pack:
    generate_model: false # because this is a block, a 2nd model pointing to specified one will be generated anyway
    model: default/caveblock
  Mechanics:
    noteblock:
      block_sounds:
        break_sound: block.glass.break
        place_sound: block.glass.place
      custom_variation: 0
      model: default/caveblock
      hardness: 1
      drop:
        silktouch: false # enable if you want to cancel loots and drop the block when a silktouch pickaxe is used
        loots:
        - oraxen_item: caveblock
          probability: 1.0

amethyst_ore:
  itemname: <gradient:#4B36B1:#6699FF>Amethyst Ore
  material: PAPER
  Pack:
    generate_model: true
    parent_model: block/cube_all
    textures:
    - default/amethyst_ore
  Mechanics:
    noteblock:
      block_sounds:
        break_sound: block.stone.break
        place_sound: block.stone.place
      custom_variation: 1
      model: amethyst_ore
      hardness: 6
      drop:
        silktouch: true
        fortune: true
        minimal_type: IRON
        best_tools:
        - PICKAXE
        loots:
        - oraxen_item: amethyst
          probability: 1.0

ruby_ore:
  itemname: <gradient:#FA7CBB:#F14658>Ruby Ore
  material: PAPER
  Pack:
    generate_model: true
    parent_model: block/cube_all
    textures:
    - default/ruby_ore
  Mechanics:
    noteblock:
      block_sounds:
        break_sound: block.stone.break
        place_sound: block.stone.place
      custom_variation: 2
      model: ruby_ore
      hardness: 7
      drop:
        silktouch: true
        fortune: true
        minimal_type: IRON
        best_tools:
        - PICKAXE
        loots:
        - oraxen_item: ruby
          probability: 1.0


onyx_ore:
  itemname: <#6f737d>Onyx Ore
  material: PAPER
  Pack:
    generate_model: true
    parent_model: block/cube_all
    textures:
    - default/onyx_ore
  Mechanics:
    noteblock:
      block_sounds:
        break_sound: block.stone.break
        place_sound: block.stone.place
      custom_variation: 3
      model: onyx_ore
      hardness: 8
      drop:
        silktouch: true
        fortune: true
        minimal_type: IRON
        best_tools:
        - PICKAXE
        loots:
        - oraxen_item: onyx
          probability: 1.0

orax_ore:
  itemname: <gradient:#3685EC:#72C1E4>Orax Ore
  material: PAPER
  Pack:
    generate_model: true
    parent_model: block/cube_all
    textures:
    - default/orax_ore
  Mechanics:
    noteblock:
      block_sounds:
        break_sound: block.stone.break
        place_sound: block.stone.place
      custom_variation: 4
      model: orax_ore
      hardness: 8
      drop:
        silktouch: true
        fortune: true
        minimal_type: IRON
        best_tools:
        - PICKAXE
        loots:
        - oraxen_item: orax
          probability: 1.0
