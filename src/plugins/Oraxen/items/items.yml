# Material list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html
# EntityType list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/EntityType.html
# Attribute list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/attribute/Attribute.html
# Potion effects list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/potion/PotionEffectType.html
# Random UUID generator: https://www.uuidgenerator.net/
# Color codes: https://docs.adventure.kyori.net/minimessage.html#format

grappling_hook:
  itemname: <gradient:#3685EC:#72C1E4>Grappling Hook
  material: FISHING_ROD
  lore:
    - <#6f737d>» <#D5D6D8>Travel in style using this Grappling Hook!
  Pack:
    generate_model: false
    model: grappling_hook
    cast_model: grappling_hook_cast
  Components:
    durability: 100
# example_sword:
#   itemname: <gradient:#FA7CBB:#F14658><bold>My Custom Sword
#   material: WOODEN_SWORD
#   injectID: false # allow Oraxen to recognise the item if true or not set

#   ItemFlags:
#   - HIDE_ENCHANTS
#   - HIDE_ATTRIBUTES
#   - HIDE_UNBREAKABLE
#   - HIDE_DESTROYS
#   - HIDE_PLACED_ON
#   - HIDE_POTION_EFFECTS

#   PersistentData:
#   - key: test:hello
#     type: STRING
#     value: Here is a value

#   AttributeModifiers:
#   - attribute: MOVEMENT_SPEED
#     amount: 0.1
#     operation: 0
#     slot: HAND
#     uuid: 7f268114-3267-4cb1-b85d-2c3e4cf14c1b
#     name: oraxen:modifier
#     key: oraxen:modifier

#   Enchantments:
#     protection: 4
#     flame: 34
#     sharpness: 18
#   Components:
#     durability: 10

# example_custom_mechanic:
#   itemname: <gradient:#FA7CBB:#F14658><bold>My Item With A Custom Mechanic
#   material: DIAMOND_SWORD
#   Mechanics:
#     custom:
#       test:
#         one_usage: true
#         cooldown: 200 # in ticks
#         event: CLICK:right:all
#         conditions:
#         - '#player.hasPermission(''example.permission'')'
#         actions:
#         - '[console] give <player> cooked_beef 1'

# example_efficient_pickaxe:
#   itemname: <gradient:#59A7EA:#F1D2FF><bold>Turbo Pickaxe
#   material: DIAMOND_PICKAXE
#   Mechanics:
#     efficiency:
#       amount: 3

# example_slow_pickaxe:
#   itemname: <gradient:#59A7EA:#F1D2FF><bold>Slow Pickaxe
#   material: DIAMOND_PICKAXE
#   Mechanics:
#     efficiency:
#       amount: -3

# example_potion:
#   itemname: <#6f737d>Wither Potion
#   material: SPLASH_POTION
#   injectID: false # allow Oraxen to recognise the item if true or not set
#   color: 34, 0, 10
#   PotionEffects:
#   - type: wither
#     duration: 200
#     amplifier: 2
#     ambient: false
#     particles: true
#     icon: true

# miner_sandwitch:
#   itemname: <gradient:#F69D84:#FAD98D>Miner's sandwitch
#   material: PAPER
#   Pack:
#     generate_model: true
#     parent_model: item/generated
#     textures:
#     - default/sandwitch.png
#   Components:
#     food:
#       nutrition: 8 # Same as cooked beef
#       saturation: 12.8 # Same as cooked beef
#       can_always_eat: true # So it can be eaten even when not hungry
#     consumable:
#       consume_seconds: 5
#       animation: eat
#       sound: entity.generic.eat
#       has_consume_particles: true
#       on_consume_effects:
#       - type: apply_effects
#         effects:
#           minecraft:haste:
#             duration: 3600
#             amplifier: 0
#             ambient: true
#             show_particles: true
#             show_icon: true
#         probability: 1.0

# amethyst:
#   itemname: <gradient:#4B36B1:#6699FF>Amethyst
#   material: PAPER
#   Pack:
#     generate_model: true
#     parent_model: item/generated
#     textures:
#     - default/amethyst.png

# ruby:
#   itemname: <gradient:#FA7CBB:#F14658>Ruby
#   material: PAPER
#   Pack:
#     generate_model: true
#     parent_model: item/generated
#     textures:
#     - default/ruby.png

# onyx:
#   itemname: <#6f737d>Onyx
#   material: PAPER
#   Pack:
#     generate_model: true
#     parent_model: item/generated
#     textures:
#     - default/onyx.png

# orax:
#   itemname: <gradient:#3685EC:#72C1E4>Orax
#   material: PAPER
#   Pack:
#     generate_model: true
#     parent_model: item/generated
#     textures:
#     - default/orax.png

# fire:
#   itemname: <gradient:#F06966:#FAD6A6>Fire
#   material: PAPER
#   Pack:
#     generate_model: false
#     model: block/fire_floor0 # so we use the already existing fire model

# welcome_disk:
#   itemname: <gradient:#9055FF:#13E2DA>Welcome Disk
#   material: PAPER
#   Components:
#     max_stack_size: 1
#     jukebox_playable:
#       show_in_tooltip: true
#       song_key: oraxen:welcome
#   Pack:
#     generate_model: true
#     parent_model: item/handheld
#     textures:
#     - default/welcome_disk.png
