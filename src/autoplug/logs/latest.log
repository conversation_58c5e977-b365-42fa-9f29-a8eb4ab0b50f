[20-07-2025 21:48:05][AP][DEBUG][AL] Started Logger(AP)
[20-07-2025 21:48:05][AP][DEBUG][Main] !!!IMPORTANT!!! -> THIS LOG-FILE CONTAINS SENSITIVE INFORMATION <- !!!IMPORTANT!!!
[20-07-2025 21:48:05][AP][DEBUG][Main] !!!IMPORTANT!!! -> THIS LOG-FILE CONTAINS SENSITIVE INFORMATION <- !!!IMPORTANT!!!
[20-07-2025 21:48:05][AP][DEBUG][Main] !!!IMPORTANT!!! -> THIS LOG-FILE CONTAINS SENSITIVE INFORMATION <- !!!IMPORTANT!!!
[20-07-2025 21:48:05][AP][DEBUG][Main] START COMMAND: nulljava -jar AutoPlug-Client.jar 
[20-07-2025 21:48:05][AP][DEBUG][Main] JAR: /run/media/glitchy/Data/Projects/mc_server/src/AutoPlug-Client.jar
[20-07-2025 21:48:05][AP][DEBUG][Main] ARGS: []
[20-07-2025 21:48:05][AP][DEBUG][Main] SYSTEM OS: Linux
[20-07-2025 21:48:05][AP][DEBUG][Main] SYSTEM OS ARCH: amd64
[20-07-2025 21:48:05][AP][DEBUG][Main] SYSTEM VERSION: 6.15.6-zen1-1-zen
[20-07-2025 21:48:05][AP][DEBUG][Main] JAVA VERSION: 21.0.8
[20-07-2025 21:48:05][AP][DEBUG][Main] JAVA VENDOR: Arch Linux https://openjdk.org/
[20-07-2025 21:48:05][AP][DEBUG][Main] JAVA DIR: /usr/lib/jvm/java-21-openjdk
[20-07-2025 21:48:05][AP][DEBUG][Main] WORKING DIR: /run/media/glitchy/Data/Projects/mc_server/src
[20-07-2025 21:48:05][AP][DEBUG][Main] TEST-MODE: false
[20-07-2025 21:48:05][AP][INFO] | ------------------------------------------- |
[20-07-2025 21:48:05][AP][INFO]      ___       __       ___  __             
[20-07-2025 21:48:05][AP][INFO]     / _ |__ __/ /____  / _ \/ /_ _____ _   
[20-07-2025 21:48:05][AP][INFO]    / __ / // / __/ _ \/ ___/ / // / _ `/   
[20-07-2025 21:48:05][AP][INFO]   /_/ |_\_,_/\__/\___/_/  /_/\_,_/\_, /
[20-07-2025 21:48:05][AP][INFO]                                  /___/    
[20-07-2025 21:48:05][AP][INFO] AutoPlug-Client 8.3.1 by Osiris-Team
[20-07-2025 21:48:05][AP][INFO] Web-Panel: https://autoplug.one/
[20-07-2025 21:48:05][AP][INFO] | ------------------------------------------- |
[20-07-2025 21:48:05][AP][DEBUG][GeneralConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/general.yml
[20-07-2025 21:48:05][AP][DEBUG][UpdaterConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/updater.yml
[20-07-2025 21:48:05][AP][DEBUG][LoggerConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logger.yml
[20-07-2025 21:48:05][AP][DEBUG][WebConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/web.yml
[20-07-2025 21:48:05][AP][DEBUG][BackupConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backup.yml
[20-07-2025 21:48:05][AP][DEBUG][RestarterConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/restarter.yml
[20-07-2025 21:48:05][AP][DEBUG][RestarterConfig] [03, 00]
[20-07-2025 21:48:05][AP][DEBUG][RestarterConfig] [03, 00]
[20-07-2025 21:48:05][AP][DEBUG][SharedFilesConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/shared-files.yml
[20-07-2025 21:48:05][AP][DEBUG][SSHConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/ssh.yml
[20-07-2025 21:48:05][AP][DEBUG][PluginsConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/plugins.yml
[20-07-2025 21:48:05][AP][DEBUG][ModsConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/mods.yml
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, autoplug] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, autoplug, auto-stop] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, autoplug, start-on-boot] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, autoplug, target-software] VAL: [MINECRAFT_SERVER] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, autoplug, system-tray, enable] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, autoplug, system-tray, theme] VAL: [darcula] DEF: [light]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server, key] VAL: NOT SHOWN DUE TO SECURITY RISK  DEF: [INSERT_KEY_HERE]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server, version] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server, auto-start] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server, auto-eula] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server, start-command] VAL: [java -Xms6144M -Xmx6144M -jar "server.jar" nogui] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server, stop-command] VAL: [stop] DEF: [stop, shutdown, end, close, finish, terminate, abort]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, server, restart-on-crash] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, directory-cleaner] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, directory-cleaner, enabled] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, directory-cleaner, max-days] VAL: [7] DEF: [7]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [general, directory-cleaner, list] VAL: [true ./autoplug/logs, ./autoplug/downloads, ./logs] DEF: [true ./autoplug/logs, ./autoplug/downloads]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, debug] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, autoplug-label] VAL: [AP] DEF: [AP]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, force-ANSI] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, color-server-log] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, tasks] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, tasks, live-tasks, enable] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, tasks, live-tasks, refresh-interval] VAL: [500] DEF: [500]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, tasks, show-warnings] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [logger, tasks, show-detailed-warnings] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, online-console] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, online-system-console] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, updater-results, send-plugins-updaters-results] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, updater-results, send-server-updaters-results] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, updater-results, send-self-updaters-results] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, send-details, public] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, send-details, private] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, send-details, ip] VAL: [127.0.0.1] DEF: [127.0.0.1]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, send-details, port] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [web, file-manager] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, max-days] VAL: [7] DEF: [7]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, cool-down] VAL: [500] DEF: [500]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, path] VAL: [./autoplug/backups] DEF: [./autoplug/backups]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, include, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, include, list] VAL: [./, ./server.properties] DEF: [./, ./server.properties]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, exclude, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, exclude, list] VAL: [./autoplug/backups, ./autoplug/downloads, ./autoplug/system, ./autoplug/logs, ./plugins/dynmap, ./plugins/WorldBorder] DEF: [./autoplug/backups, ./autoplug/downloads, ./autoplug/system, ./autoplug/logs, ./plugins/dynmap, ./plugins/WorldBorder]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, enable] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, delete-on-complete] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, host] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, port] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, username] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, password] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, path] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [backup, upload, rsa-key-path] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, times] VAL: [03:00] DEF: [05:00]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 10] VAL: [say Restarting in 10 seconds., say Please allow up to 2min for this process to complete.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 3] VAL: [say Restarting in 3.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 2] VAL: [say Restarting in 2.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 1] VAL: [say Restarting in 1.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 0] VAL: [say Restarting...] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, enable] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, cron] VAL: [0 30 9 * * ? *] DEF: [0 30 9 * * ? *]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 10] VAL: [say Restarting in 10 seconds., say Please allow up to 2min for this process to complete.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 3] VAL: [say Restarting in 3.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 2] VAL: [say Restarting in 2.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 1] VAL: [say Restarting in 1.] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 0] VAL: [say Restarting...] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, global-cool-down] VAL: [60] DEF: [60]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, global-recurring-checks] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, global-recurring-checks, enable] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, global-recurring-checks, intervall] VAL: [12] DEF: [12]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, self-updater] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, self-updater, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, self-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, self-updater, build] VAL: [stable] DEF: [stable]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, java-updater] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, java-updater, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, java-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, java-updater, version] VAL: [21] DEF: [21]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, java-updater, build-id] VAL: [9] DEF: [0]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, java-updater, large-heap] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, software] VAL: [purpur] DEF: [paper]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, steam-cmd-login] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, version] VAL: [1.21.7] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, build-id] VAL: [0] DEF: [0]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, skip-hash-check] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, github, repo-name] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, github, asset-name] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, github, version] VAL: [0] DEF: [0]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, jenkins, project-url] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, jenkins, artifact-name] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, jenkins, build-id] VAL: [0] DEF: [0]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater, enable] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater, path] VAL: [./plugins] DEF: [./plugins]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater, version] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater, async] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater, web-database, enable] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, plugins-updater, web-database, min-usages] VAL: [50] DEF: [50]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, mods-updater] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, mods-updater, enable] VAL: [false] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, mods-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, mods-updater, path] VAL: [./mods] DEF: [./mods]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, mods-updater, version] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, mods-updater, async] VAL: [true] DEF: [true]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [updater, mods-updater, check-name-for-mod-loader] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [shared-files] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [shared-files, enable] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [shared-files, copy-from] VAL: [./plugins, ./server.jar] DEF: [./plugins, ./server.jar]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [shared-files, send-to] VAL: [C:\User\Peter\servers\my-second-server, ./servers/another-server] DEF: [C:\User\Peter\servers\my-second-server, ./servers/another-server]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh, enabled] VAL: [false] DEF: [false]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh, port] VAL: [22] DEF: [22]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh, auth-method] VAL: [key-only] DEF: [key-only]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh, allowed-keys-path] VAL: [./autoplug/allowed_ssh_keys.txt] DEF: [./autoplug/allowed_ssh_keys.txt]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh, server-private-key] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh, username] VAL: [autoplug] DEF: [autoplug]
[20-07-2025 21:48:05][AP][DEBUG][UtilsConfig] [ssh, password] VAL: [] DEF: []
[20-07-2025 21:48:05][AP][INFO] Checked configs, took 168ms
[20-07-2025 21:48:05][AP][DEBUG][SystemConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/config.yml
[20-07-2025 21:48:05][AP][INFO] Authenticating server...
[20-07-2025 21:48:05][AP][DEBUG][ConMain] open()
[20-07-2025 21:48:05][AP][DEBUG][ConMain] _close()
[20-07-2025 21:48:05][AP][WARN] ================================
[20-07-2025 21:48:05][AP][WARN] Message: null
[20-07-2025 21:48:05][AP][WARN] Details: No valid key provided. Register your server at https://autoplug.one/, get your server-key and add it to the /autoplug/general.yml config file. Enter '.con reload' to retry.
[20-07-2025 21:48:05][AP][WARN] Type: java.security.InvalidKeyException
[20-07-2025 21:48:05][AP][WARN] Stacktrace: 
[20-07-2025 21:48:05][AP][WARN] com.osiris.autoplug.client.network.online.DefaultConnection._open(DefaultConnection.java:123)
[20-07-2025 21:48:05][AP][WARN] com.osiris.autoplug.client.network.online.DefaultConnection.open(DefaultConnection.java:104)
[20-07-2025 21:48:05][AP][WARN] com.osiris.autoplug.client.network.online.ConMain.open(ConMain.java:49)
[20-07-2025 21:48:05][AP][WARN] com.osiris.autoplug.client.Main.main(Main.java:300)
[20-07-2025 21:48:05][AP][WARN] ================================
[20-07-2025 21:48:05][AP][INFO] Initialised successfully.
[20-07-2025 21:48:05][AP][INFO] | ------------------------------------------- |
[20-07-2025 21:48:05][AP][INFO] Enter .help for a list of all commands.
[20-07-2025 21:48:05][AP][DEBUG][ConPluginCommandReceive] Binding on localhost:35565 for AutoPlug-Plugin...
[20-07-2025 21:48:05][AP][DEBUG][ConPluginCommandReceive] Success!
[20-07-2025 21:48:05][AP][DEBUG][ConPluginCommandReceive] Waiting for AutoPlug-Plugin to connect...
[20-07-2025 21:48:05][AP][INFO] Running pre-startup tasks, please be patient...
[20-07-2025 21:48:05][AP][INFO] [TASK] [\] > [SelfUpdater] Initialising...
[20-07-2025 21:48:05][AP][INFO] 
[20-07-2025 21:48:07][AP][DEBUG][TaskBackup] Excluded 'backups' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups
[20-07-2025 21:48:07][AP][DEBUG][TaskBackup] Excluded 'downloads' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads
[20-07-2025 21:48:07][AP][DEBUG][TaskBackup] Excluded 'system' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system
[20-07-2025 21:48:07][AP][DEBUG][TaskBackup] Excluded 'logs' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logs
[20-07-2025 21:48:07][AP][DEBUG][TaskBackup] Excluded 'dynmap' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/dynmap
[20-07-2025 21:48:07][AP][DEBUG][TaskBackup] Excluded 'WorldBorder' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/WorldBorder
[20-07-2025 21:48:10][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:10][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/LuckPerms/libs/asm-commons-9.8.jar 0% - ADD_ENTRY
[20-07-2025 21:48:10][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[20-07-2025 21:48:10][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[20-07-2025 21:48:10][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[20-07-2025 21:48:10][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[20-07-2025 21:48:10][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[20-07-2025 21:48:10][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[20-07-2025 21:48:10][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[20-07-2025 21:48:10][AP][INFO] 
[20-07-2025 21:48:15][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:15][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/server.jar 0% - ADD_ENTRY
[20-07-2025 21:48:15][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[20-07-2025 21:48:15][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[20-07-2025 21:48:15][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[20-07-2025 21:48:15][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[20-07-2025 21:48:15][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[20-07-2025 21:48:15][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[20-07-2025 21:48:15][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[20-07-2025 21:48:15][AP][INFO] 
[20-07-2025 21:48:20][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:20][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/versions/1.21.7/purpur-1.21.7.jar 0% - ADD_ENTRY
[20-07-2025 21:48:20][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[20-07-2025 21:48:20][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[20-07-2025 21:48:20][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[20-07-2025 21:48:20][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[20-07-2025 21:48:20][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[20-07-2025 21:48:20][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[20-07-2025 21:48:20][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[20-07-2025 21:48:20][AP][INFO] 
[20-07-2025 21:48:21][AP][DEBUG][TaskBackup] Created backup at: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups/2025-07-20-21.48-BACKUP.zip
[20-07-2025 21:48:21][AP][DEBUG][RestarterConfig] [03, 00]
[20-07-2025 21:48:21][AP][DEBUG][RestarterConfig] [03, 00]
[20-07-2025 21:48:21][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[20-07-2025 21:48:21][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[20-07-2025 21:48:21][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[20-07-2025 21:48:21][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[20-07-2025 21:48:21][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[20-07-2025 21:48:21][AP][DEBUG][UtilsMinecraft] 1.21.7
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 21:48:21][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[20-07-2025 21:48:21][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[20-07-2025 21:48:21][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 21:48:21][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 21:48:21][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[20-07-2025 21:48:21][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[20-07-2025 21:48:21][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[20-07-2025 21:48:22][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[20-07-2025 21:48:22][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[20-07-2025 21:48:22][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[20-07-2025 21:48:22][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[20-07-2025 21:48:23][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[20-07-2025 21:48:23][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[20-07-2025 21:48:23][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[20-07-2025 21:48:23][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[20-07-2025 21:48:23][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[20-07-2025 21:48:23][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[20-07-2025 21:48:23][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[20-07-2025 21:48:24][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[20-07-2025 21:48:25][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[20-07-2025 21:48:25][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 21:48:25][AP][INFO] [TASK] [|] > [PluginsUpdater] Checked 'EcoMobs' plugin (12/16)
[20-07-2025 21:48:25][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 21:48:25][AP][INFO] 
[20-07-2025 21:48:25][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[20-07-2025 21:48:25][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[20-07-2025 21:48:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[20-07-2025 21:48:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[20-07-2025 21:48:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[20-07-2025 21:48:26][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[20-07-2025 21:48:27][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[20-07-2025 21:48:27][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[20-07-2025 21:48:27][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[20-07-2025 21:48:27][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[20-07-2025 21:48:27][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[20-07-2025 21:48:28][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[20-07-2025 21:48:29][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[20-07-2025 21:48:29][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[20-07-2025 21:48:29][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 21:48:30][AP][INFO] [TASK] [/] > [PluginsUpdater] Checked 'eco' plugin (15/16)
[20-07-2025 21:48:30][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 21:48:30][AP][INFO] 
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[20-07-2025 21:48:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 21:48:31][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[20-07-2025 21:48:31][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 21:48:31][AP][INFO] 
[20-07-2025 21:48:31][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[20-07-2025 21:48:31][AP][INFO] [OK][BackupTask] Completed backup & skipped upload.
[20-07-2025 21:48:31][AP][INFO] [OK][GeneralTasks] Finished.
[20-07-2025 21:48:31][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[20-07-2025 21:48:31][AP][INFO] [OK][CustomRestarter] Skipped.
[20-07-2025 21:48:31][AP][INFO] [OK][JavaUpdater] Your Java installation is on the latest version!
[20-07-2025 21:48:31][AP][INFO] [OK][ServerUpdater] Up-to-date version
[20-07-2025 21:48:31][AP][INFO] [OK][PluginsUpdater] Checked 16/16 plugins.
[20-07-2025 21:48:31][AP][INFO] [OK][ModsUpdater] Skipped.
[20-07-2025 21:48:31][AP][INFO] GeneralTasks:
[20-07-2025 21:48:31][AP][INFO] Directory cleaner removed 5 files, from directory ./autoplug/logs
[20-07-2025 21:48:31][AP][INFO] Directory cleaner removed 2 files, from directory ./autoplug/downloads
[20-07-2025 21:48:31][AP][INFO] Directory cleaner removed 9 files, from directory ./logs
[20-07-2025 21:48:31][AP][INFO] PluginsUpdater:
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] Message: There was an api-error for RoseResourcepack!
[20-07-2025 21:48:31][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 21:48:31][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 21:48:31][AP][WARN] Stacktrace: 
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 21:48:31][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 21:48:31][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] Message: There was an api-error for OpenJS!
[20-07-2025 21:48:31][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 21:48:31][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 21:48:31][AP][WARN] Stacktrace: 
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 21:48:31][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 21:48:31][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 21:48:31][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 21:48:31][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] Message: null
[20-07-2025 21:48:31][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[20-07-2025 21:48:31][AP][WARN] Type: java.lang.Exception
[20-07-2025 21:48:31][AP][WARN] Stacktrace: 
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[20-07-2025 21:48:31][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] Message: null
[20-07-2025 21:48:31][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[20-07-2025 21:48:31][AP][WARN] Type: java.lang.Exception
[20-07-2025 21:48:31][AP][WARN] Stacktrace: 
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[20-07-2025 21:48:31][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][WARN] Message: null
[20-07-2025 21:48:31][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[20-07-2025 21:48:31][AP][WARN] Type: java.lang.Exception
[20-07-2025 21:48:31][AP][WARN] Stacktrace: 
[20-07-2025 21:48:31][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[20-07-2025 21:48:31][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[20-07-2025 21:48:31][AP][WARN] ================================
[20-07-2025 21:48:31][AP][INFO] Starting server: server.jar
[20-07-2025 21:48:31][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[20-07-2025 21:48:31][AP][DEBUG][Server] process: Process[pid=191750, exitValue="not exited"]
[20-07-2025 21:48:31][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@65b3f4a4
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 21:48:35][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 21:48:35][AP][INFO] 
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 21:48:35][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[20-07-2025 21:48:35][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 21:48:35][AP][INFO] 
[20-07-2025 23:41:03][AP][INFO] Server was stopped.
[20-07-2025 23:41:04][AP][INFO] To stop AutoPlug too, enter '.stop both'.
[20-07-2025 23:43:36][AP][INFO] Running pre-startup tasks, please be patient...
[20-07-2025 23:43:36][AP][INFO] [TASK] [\] > [SelfUpdater] Initialising...
[20-07-2025 23:43:36][AP][INFO] [TASK] [|] > [BackupTask] Initialising...
[20-07-2025 23:43:36][AP][INFO] 
[20-07-2025 23:43:38][AP][DEBUG][RestarterConfig] [03, 00]
[20-07-2025 23:43:38][AP][DEBUG][RestarterConfig] [03, 00]
[20-07-2025 23:43:38][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[20-07-2025 23:43:38][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[20-07-2025 23:43:38][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[20-07-2025 23:43:38][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[20-07-2025 23:43:38][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[20-07-2025 23:43:38][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[20-07-2025 23:43:38][AP][DEBUG][UtilsMinecraft] 1.21.7
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[20-07-2025 23:43:38][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[20-07-2025 23:43:38][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[20-07-2025 23:43:38][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[20-07-2025 23:43:38][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[20-07-2025 23:43:39][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[20-07-2025 23:43:39][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[20-07-2025 23:43:39][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[20-07-2025 23:43:39][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[20-07-2025 23:43:39][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[20-07-2025 23:43:40][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [BackupTask] Skipped. Cool-down still active (384 minutes remaining).
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 23:43:41][AP][INFO] [TASK] [/] > [PluginsUpdater] Checked 'PlaceholderAPI' plugin (8/16)
[20-07-2025 23:43:41][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 23:43:41][AP][INFO] 
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[20-07-2025 23:43:41][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[20-07-2025 23:43:42][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[20-07-2025 23:43:42][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[20-07-2025 23:43:42][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[20-07-2025 23:43:42][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[20-07-2025 23:43:43][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[20-07-2025 23:43:43][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[20-07-2025 23:43:43][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[20-07-2025 23:43:43][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[20-07-2025 23:43:44][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[20-07-2025 23:43:45][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[20-07-2025 23:43:45][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[20-07-2025 23:43:45][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[20-07-2025 23:43:45][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[20-07-2025 23:43:45][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[20-07-2025 23:43:45][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [BackupTask] Skipped. Cool-down still active (384 minutes remaining).
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 23:43:46][AP][INFO] [TASK] [-] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (14/16)
[20-07-2025 23:43:46][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 23:43:46][AP][INFO] 
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[20-07-2025 23:43:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[20-07-2025 23:43:47][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [BackupTask] Skipped. Cool-down still active (384 minutes remaining).
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 23:43:48][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[20-07-2025 23:43:48][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 23:43:48][AP][INFO] 
[20-07-2025 23:43:48][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[20-07-2025 23:43:48][AP][INFO] [OK][BackupTask] Skipped. Cool-down still active (384 minutes remaining).
[20-07-2025 23:43:48][AP][INFO] [OK][GeneralTasks] Finished.
[20-07-2025 23:43:48][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[20-07-2025 23:43:48][AP][INFO] [OK][CustomRestarter] Skipped.
[20-07-2025 23:43:48][AP][INFO] [OK][JavaUpdater] Your Java installation is on the latest version!
[20-07-2025 23:43:48][AP][INFO] [OK][ServerUpdater] Up-to-date version
[20-07-2025 23:43:48][AP][INFO] [OK][PluginsUpdater] Checked 16/16 plugins.
[20-07-2025 23:43:48][AP][INFO] [OK][ModsUpdater] Skipped.
[20-07-2025 23:43:48][AP][INFO] GeneralTasks:
[20-07-2025 23:43:48][AP][INFO] Directory cleaner removed 1 files, from directory ./autoplug/logs
[20-07-2025 23:43:48][AP][INFO] Directory cleaner removed 1 files, from directory ./logs
[20-07-2025 23:43:48][AP][INFO] PluginsUpdater:
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] Message: There was an api-error for RoseResourcepack!
[20-07-2025 23:43:48][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 23:43:48][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 23:43:48][AP][WARN] Stacktrace: 
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 23:43:48][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 23:43:48][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] Message: There was an api-error for OpenJS!
[20-07-2025 23:43:48][AP][WARN] Details: Index 0 out of bounds for length 0
[20-07-2025 23:43:48][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[20-07-2025 23:43:48][AP][WARN] Stacktrace: 
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[20-07-2025 23:43:48][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[20-07-2025 23:43:48][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[20-07-2025 23:43:48][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[20-07-2025 23:43:48][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] Message: null
[20-07-2025 23:43:48][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[20-07-2025 23:43:48][AP][WARN] Type: java.lang.Exception
[20-07-2025 23:43:48][AP][WARN] Stacktrace: 
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[20-07-2025 23:43:48][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] Message: null
[20-07-2025 23:43:48][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[20-07-2025 23:43:48][AP][WARN] Type: java.lang.Exception
[20-07-2025 23:43:48][AP][WARN] Stacktrace: 
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[20-07-2025 23:43:48][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][WARN] Message: null
[20-07-2025 23:43:48][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[20-07-2025 23:43:48][AP][WARN] Type: java.lang.Exception
[20-07-2025 23:43:48][AP][WARN] Stacktrace: 
[20-07-2025 23:43:48][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[20-07-2025 23:43:48][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[20-07-2025 23:43:48][AP][WARN] ================================
[20-07-2025 23:43:48][AP][INFO] Starting server: server.jar
[20-07-2025 23:43:48][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[20-07-2025 23:43:48][AP][DEBUG][Server] process: Process[pid=202388, exitValue="not exited"]
[20-07-2025 23:43:48][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@40fcc0a


.s

[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [BackupTask] Skipped. Cool-down still active (384 minutes remaining).
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 23:43:51][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 23:43:51][AP][INFO] 
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [BackupTask] Skipped. Cool-down still active (384 minutes remaining).
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[20-07-2025 23:43:51][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[20-07-2025 23:43:51][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[20-07-2025 23:43:51][AP][INFO] 
[20-07-2025 23:44:36][AP][INFO] Server was stopped.
[20-07-2025 23:44:36][AP][INFO] To stop AutoPlug too, enter '.stop both'.
[21-07-2025 03:00:00][AP][WARN] ================================
[21-07-2025 03:00:00][AP][WARN] Message: Error while executing restart!
[21-07-2025 03:00:00][AP][WARN] Details: Server is not running. Restart not possible.
[21-07-2025 03:00:00][AP][WARN] Type: java.lang.Exception
[21-07-2025 03:00:00][AP][WARN] Stacktrace: 
[21-07-2025 03:00:00][AP][WARN] com.osiris.autoplug.client.tasks.scheduler.RestartJob.execute(RestartJob.java:31)
[21-07-2025 03:00:00][AP][WARN] org.quartz.core.JobRunShell.run(JobRunShell.java:202)
[21-07-2025 03:00:00][AP][WARN] org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
[21-07-2025 03:00:00][AP][WARN] ================================
[21-07-2025 13:14:28][AP][INFO] Stopping server...
[21-07-2025 13:14:28][AP][DEBUG][Server] process: Process[pid=202388, exitValue=0]
[21-07-2025 13:14:28][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@40fcc0a
[21-07-2025 13:14:28][AP][WARN] ================================
[21-07-2025 13:14:28][AP][WARN] Message: Server not running!
[21-07-2025 13:14:28][AP][WARN] Details: null
[21-07-2025 13:14:28][AP][WARN] ================================


.st

[21-07-2025 13:14:30][AP][INFO] Running pre-startup tasks, please be patient...
[21-07-2025 13:14:30][AP][INFO] [TASK] [\] > [SelfUpdater] Initialising...
[21-07-2025 13:14:30][AP][INFO] [TASK] [|] > [BackupTask] Initialising...
[21-07-2025 13:14:30][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[21-07-2025 13:14:30][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[21-07-2025 13:14:30][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[21-07-2025 13:14:30][AP][INFO] 
[21-07-2025 13:14:32][AP][DEBUG][TaskBackup] Excluded 'backups' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups
[21-07-2025 13:14:32][AP][DEBUG][TaskBackup] Excluded 'downloads' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads
[21-07-2025 13:14:32][AP][DEBUG][TaskBackup] Excluded 'system' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system
[21-07-2025 13:14:32][AP][DEBUG][TaskBackup] Excluded 'logs' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logs
[21-07-2025 13:14:32][AP][DEBUG][TaskBackup] Excluded 'dynmap' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/dynmap
[21-07-2025 13:14:32][AP][DEBUG][TaskBackup] Excluded 'WorldBorder' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/WorldBorder
[21-07-2025 13:14:35][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[21-07-2025 13:14:35][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/CustomCrops/libs/zstd-jni-1.5.6-9.jar 0% - ADD_ENTRY
[21-07-2025 13:14:35][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[21-07-2025 13:14:35][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[21-07-2025 13:14:35][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[21-07-2025 13:14:35][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[21-07-2025 13:14:35][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[21-07-2025 13:14:35][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[21-07-2025 13:14:35][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[21-07-2025 13:14:35][AP][INFO] 
[21-07-2025 13:14:40][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[21-07-2025 13:14:40][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/logs/latest.log 0% - ADD_ENTRY
[21-07-2025 13:14:40][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[21-07-2025 13:14:40][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[21-07-2025 13:14:40][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[21-07-2025 13:14:40][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[21-07-2025 13:14:40][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[21-07-2025 13:14:40][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[21-07-2025 13:14:40][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[21-07-2025 13:14:40][AP][INFO] 
[21-07-2025 13:14:45][AP][DEBUG][TaskBackup] Created backup at: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups/2025-07-21-13.14-BACKUP.zip
[21-07-2025 13:14:45][AP][DEBUG][RestarterConfig] [03, 00]
[21-07-2025 13:14:45][AP][DEBUG][RestarterConfig] [03, 00]
[21-07-2025 13:14:45][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[21-07-2025 13:14:45][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[21-07-2025 13:14:45][AP][INFO] [TASK] [|] > [GeneralTasks] Cleaning selected directories...
[21-07-2025 13:14:45][AP][INFO] [TASK] [/] > [DailyRestarter] Scheduler already running. Put into standby.
[21-07-2025 13:14:45][AP][INFO] [TASK] [-] > [CustomRestarter] Initialising...
[21-07-2025 13:14:45][AP][INFO] [TASK] [\] > [JavaUpdater] Initialising...
[21-07-2025 13:14:45][AP][INFO] [TASK] [|] > [ServerUpdater] Initialising...
[21-07-2025 13:14:45][AP][INFO] [TASK] [/] > [PluginsUpdater] Initialising...
[21-07-2025 13:14:45][AP][INFO] [TASK] [-] > [ModsUpdater] Initialising...
[21-07-2025 13:14:45][AP][INFO] 
[21-07-2025 13:14:45][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[21-07-2025 13:14:45][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[21-07-2025 13:14:45][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[21-07-2025 13:14:45][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[21-07-2025 13:14:45][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[21-07-2025 13:14:45][AP][DEBUG][UtilsMinecraft] 1.21.7
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[21-07-2025 13:14:45][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[21-07-2025 13:14:45][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[21-07-2025 13:14:45][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[21-07-2025 13:14:45][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[21-07-2025 13:14:45][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[21-07-2025 13:14:46][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[21-07-2025 13:14:46][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[21-07-2025 13:14:46][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[21-07-2025 13:14:46][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[21-07-2025 13:14:47][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[21-07-2025 13:14:48][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[21-07-2025 13:14:48][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[21-07-2025 13:14:48][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[21-07-2025 13:14:48][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[21-07-2025 13:14:49][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[21-07-2025 13:14:49][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[21-07-2025 13:14:49][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[21-07-2025 13:14:49][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[21-07-2025 13:14:49][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[21-07-2025 13:14:49][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[21-07-2025 13:14:49][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[21-07-2025 13:14:50][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[21-07-2025 13:14:50][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[21-07-2025 13:14:50][AP][INFO] [TASK] [\] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (13/16)
[21-07-2025 13:14:50][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[21-07-2025 13:14:50][AP][INFO] 
[21-07-2025 13:14:50][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[21-07-2025 13:14:50][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[21-07-2025 13:14:51][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[21-07-2025 13:14:52][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[21-07-2025 13:14:52][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[21-07-2025 13:14:52][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[21-07-2025 13:14:53][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[21-07-2025 13:14:54][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[21-07-2025 13:14:54][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[21-07-2025 13:14:54][AP][INFO] 
[21-07-2025 13:14:54][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[21-07-2025 13:14:54][AP][INFO] [OK][BackupTask] Completed backup & skipped upload.
[21-07-2025 13:14:54][AP][INFO] [OK][GeneralTasks] Finished.
[21-07-2025 13:14:54][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[21-07-2025 13:14:54][AP][INFO] [OK][CustomRestarter] Skipped.
[21-07-2025 13:14:54][AP][INFO] [OK][JavaUpdater] Your Java installation is on the latest version!
[21-07-2025 13:14:54][AP][INFO] [OK][ServerUpdater] Up-to-date version
[21-07-2025 13:14:54][AP][INFO] [OK][PluginsUpdater] Checked 16/16 plugins.
[21-07-2025 13:14:54][AP][INFO] [OK][ModsUpdater] Skipped.
[21-07-2025 13:14:54][AP][INFO] GeneralTasks:
[21-07-2025 13:14:54][AP][INFO] Directory cleaner removed 1 files, from directory ./autoplug/downloads
[21-07-2025 13:14:54][AP][INFO] Directory cleaner removed 2 files, from directory ./logs
[21-07-2025 13:14:54][AP][INFO] PluginsUpdater:
[21-07-2025 13:14:54][AP][WARN] ================================
[21-07-2025 13:14:54][AP][WARN] Message: There was an api-error for RoseResourcepack!
[21-07-2025 13:14:54][AP][WARN] Details: Index 0 out of bounds for length 0
[21-07-2025 13:14:54][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[21-07-2025 13:14:54][AP][WARN] Stacktrace: 
[21-07-2025 13:14:54][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[21-07-2025 13:14:54][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[21-07-2025 13:14:54][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[21-07-2025 13:14:54][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[21-07-2025 13:14:54][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[21-07-2025 13:14:54][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[21-07-2025 13:14:54][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[21-07-2025 13:14:54][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] Message: There was an api-error for OpenJS!
[21-07-2025 13:14:55][AP][WARN] Details: Index 0 out of bounds for length 0
[21-07-2025 13:14:55][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[21-07-2025 13:14:55][AP][WARN] Stacktrace: 
[21-07-2025 13:14:55][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[21-07-2025 13:14:55][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[21-07-2025 13:14:55][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[21-07-2025 13:14:55][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[21-07-2025 13:14:55][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[21-07-2025 13:14:55][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] Message: null
[21-07-2025 13:14:55][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[21-07-2025 13:14:55][AP][WARN] Type: java.lang.Exception
[21-07-2025 13:14:55][AP][WARN] Stacktrace: 
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[21-07-2025 13:14:55][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] Message: null
[21-07-2025 13:14:55][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[21-07-2025 13:14:55][AP][WARN] Type: java.lang.Exception
[21-07-2025 13:14:55][AP][WARN] Stacktrace: 
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[21-07-2025 13:14:55][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][WARN] Message: null
[21-07-2025 13:14:55][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[21-07-2025 13:14:55][AP][WARN] Type: java.lang.Exception
[21-07-2025 13:14:55][AP][WARN] Stacktrace: 
[21-07-2025 13:14:55][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[21-07-2025 13:14:55][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[21-07-2025 13:14:55][AP][WARN] ================================
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[21-07-2025 13:14:55][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[21-07-2025 13:14:55][AP][INFO] 
[21-07-2025 13:14:55][AP][INFO] Starting server: server.jar
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[21-07-2025 13:14:55][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[21-07-2025 13:14:55][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[21-07-2025 13:14:55][AP][INFO] 
[21-07-2025 13:14:55][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[21-07-2025 13:14:55][AP][DEBUG][Server] process: Process[pid=230306, exitValue="not exited"]
[21-07-2025 13:14:55][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@7d03bcdc


.s

[22-07-2025 03:00:00][AP][DEBUG][RestarterConfig] [03, 00]
[22-07-2025 03:00:00][AP][INFO] Executing scheduled restart in 10sec(s)...
[22-07-2025 03:00:11][AP][INFO] Restarting server...
[22-07-2025 03:00:11][AP][INFO] Stopping server...
[22-07-2025 03:00:11][AP][DEBUG][Server] process: Process[pid=230306, exitValue="not exited"]
[22-07-2025 03:00:11][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@7d03bcdc
[22-07-2025 03:00:11][AP][DEBUG][Server] Stopping server with command: "stop"
[22-07-2025 03:00:15][AP][INFO] Running pre-startup tasks, please be patient...
[22-07-2025 03:00:15][AP][INFO] 
[22-07-2025 03:00:15][AP][INFO] Server was stopped.
[22-07-2025 03:00:15][AP][INFO] To stop AutoPlug too, enter '.stop both'.
[22-07-2025 03:00:17][AP][DEBUG][TaskBackup] Excluded 'backups' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups
[22-07-2025 03:00:17][AP][DEBUG][TaskBackup] Excluded 'downloads' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads
[22-07-2025 03:00:17][AP][DEBUG][TaskBackup] Excluded 'system' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system
[22-07-2025 03:00:17][AP][DEBUG][TaskBackup] Excluded 'logs' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logs
[22-07-2025 03:00:17][AP][DEBUG][TaskBackup] Excluded 'dynmap' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/dynmap
[22-07-2025 03:00:17][AP][DEBUG][TaskBackup] Excluded 'WorldBorder' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/WorldBorder
[22-07-2025 03:00:20][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 03:00:20][AP][INFO] [TASK] [\] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/LuckPerms/libs/adventure-4.21.1.jar 0% - ADD_ENTRY
[22-07-2025 03:00:20][AP][INFO] [TASK] [|] > [GeneralTasks] Initialising...
[22-07-2025 03:00:20][AP][INFO] [TASK] [/] > [DailyRestarter] Initialising...
[22-07-2025 03:00:20][AP][INFO] [TASK] [-] > [CustomRestarter] Initialising...
[22-07-2025 03:00:20][AP][INFO] [TASK] [\] > [JavaUpdater] Initialising...
[22-07-2025 03:00:20][AP][INFO] [TASK] [|] > [ServerUpdater] Initialising...
[22-07-2025 03:00:20][AP][INFO] [TASK] [/] > [PluginsUpdater] Initialising...
[22-07-2025 03:00:20][AP][INFO] [TASK] [-] > [ModsUpdater] Initialising...
[22-07-2025 03:00:20][AP][INFO] 
[22-07-2025 03:00:25][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 03:00:25][AP][INFO] [TASK] [\] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/logs/2025-07-17-3.log.gz 0% - ADD_ENTRY
[22-07-2025 03:00:25][AP][INFO] [TASK] [|] > [GeneralTasks] Initialising...
[22-07-2025 03:00:25][AP][INFO] [TASK] [/] > [DailyRestarter] Initialising...
[22-07-2025 03:00:25][AP][INFO] [TASK] [-] > [CustomRestarter] Initialising...
[22-07-2025 03:00:25][AP][INFO] [TASK] [\] > [JavaUpdater] Initialising...
[22-07-2025 03:00:25][AP][INFO] [TASK] [|] > [ServerUpdater] Initialising...
[22-07-2025 03:00:25][AP][INFO] [TASK] [/] > [PluginsUpdater] Initialising...
[22-07-2025 03:00:25][AP][INFO] [TASK] [-] > [ModsUpdater] Initialising...
[22-07-2025 03:00:25][AP][INFO] 
[22-07-2025 03:00:29][AP][DEBUG][TaskBackup] Created backup at: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups/2025-07-22-03.00-BACKUP.zip
[22-07-2025 03:00:30][AP][DEBUG][RestarterConfig] [03, 00]
[22-07-2025 03:00:30][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 03:00:30][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 03:00:30][AP][INFO] [TASK] [\] > [GeneralTasks] Initialising...
[22-07-2025 03:00:30][AP][INFO] [TASK] [|] > [DailyRestarter] Initialising...
[22-07-2025 03:00:30][AP][INFO] [TASK] [/] > [CustomRestarter] Initialising...
[22-07-2025 03:00:30][AP][INFO] [TASK] [-] > [JavaUpdater] Initialising...
[22-07-2025 03:00:30][AP][INFO] [TASK] [\] > [ServerUpdater] Initialising...
[22-07-2025 03:00:30][AP][INFO] [TASK] [|] > [PluginsUpdater] Initialising...
[22-07-2025 03:00:30][AP][INFO] [TASK] [/] > [ModsUpdater] Initialising...
[22-07-2025 03:00:30][AP][INFO] 
[22-07-2025 03:00:30][AP][DEBUG][RestarterConfig] [03, 00]
[22-07-2025 03:00:30][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[22-07-2025 03:00:30][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[22-07-2025 03:00:30][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[22-07-2025 03:00:30][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[22-07-2025 03:00:30][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[22-07-2025 03:00:30][AP][DEBUG][UtilsMinecraft] 1.21.7
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[22-07-2025 03:00:30][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[22-07-2025 03:00:30][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[22-07-2025 03:00:30][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 03:00:30][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[22-07-2025 03:00:30][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[22-07-2025 03:00:30][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[22-07-2025 03:00:30][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[22-07-2025 03:00:31][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[22-07-2025 03:00:31][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[22-07-2025 03:00:31][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[22-07-2025 03:00:32][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[22-07-2025 03:00:33][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[22-07-2025 03:00:34][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[22-07-2025 03:00:34][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[22-07-2025 03:00:34][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[22-07-2025 03:00:34][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[22-07-2025 03:00:34][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 03:00:35][AP][INFO] [TASK] [-] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (14/16)
[22-07-2025 03:00:35][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 03:00:35][AP][INFO] 
[22-07-2025 03:00:35][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[22-07-2025 03:00:35][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[22-07-2025 03:00:35][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[22-07-2025 03:00:35][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[22-07-2025 03:00:36][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[22-07-2025 03:00:37][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[22-07-2025 03:00:37][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[22-07-2025 03:00:37][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[22-07-2025 03:00:37][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[22-07-2025 03:00:37][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[22-07-2025 03:00:37][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[22-07-2025 03:00:38][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[22-07-2025 03:00:39][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 03:00:40][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 03:00:40][AP][INFO] 
[22-07-2025 03:00:40][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [OK][BackupTask] Completed backup & skipped upload.
[22-07-2025 03:00:40][AP][INFO] [OK][GeneralTasks] Finished.
[22-07-2025 03:00:40][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[22-07-2025 03:00:40][AP][INFO] [OK][CustomRestarter] Skipped.
[22-07-2025 03:00:40][AP][INFO] [OK][JavaUpdater] Your Java installation is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [OK][ServerUpdater] Up-to-date version
[22-07-2025 03:00:40][AP][INFO] [OK][PluginsUpdater] Checked 16/16 plugins.
[22-07-2025 03:00:40][AP][INFO] [OK][ModsUpdater] Skipped.
[22-07-2025 03:00:40][AP][INFO] GeneralTasks:
[22-07-2025 03:00:40][AP][INFO] Directory cleaner removed 6 files, from directory ./autoplug/logs
[22-07-2025 03:00:40][AP][INFO] Directory cleaner removed 6 files, from directory ./logs
[22-07-2025 03:00:40][AP][INFO] PluginsUpdater:
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] Message: There was an api-error for RoseResourcepack!
[22-07-2025 03:00:40][AP][WARN] Details: Index 0 out of bounds for length 0
[22-07-2025 03:00:40][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[22-07-2025 03:00:40][AP][WARN] Stacktrace: 
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[22-07-2025 03:00:40][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[22-07-2025 03:00:40][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] Message: There was an api-error for OpenJS!
[22-07-2025 03:00:40][AP][WARN] Details: Index 0 out of bounds for length 0
[22-07-2025 03:00:40][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[22-07-2025 03:00:40][AP][WARN] Stacktrace: 
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[22-07-2025 03:00:40][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[22-07-2025 03:00:40][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[22-07-2025 03:00:40][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[22-07-2025 03:00:40][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] Message: null
[22-07-2025 03:00:40][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[22-07-2025 03:00:40][AP][WARN] Type: java.lang.Exception
[22-07-2025 03:00:40][AP][WARN] Stacktrace: 
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[22-07-2025 03:00:40][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] Message: null
[22-07-2025 03:00:40][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[22-07-2025 03:00:40][AP][WARN] Type: java.lang.Exception
[22-07-2025 03:00:40][AP][WARN] Stacktrace: 
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[22-07-2025 03:00:40][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][WARN] Message: null
[22-07-2025 03:00:40][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[22-07-2025 03:00:40][AP][WARN] Type: java.lang.Exception
[22-07-2025 03:00:40][AP][WARN] Stacktrace: 
[22-07-2025 03:00:40][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[22-07-2025 03:00:40][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[22-07-2025 03:00:40][AP][WARN] ================================
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 03:00:40][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 03:00:40][AP][INFO] 
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 03:00:40][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[22-07-2025 03:00:40][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 03:00:40][AP][INFO] 
[22-07-2025 03:00:40][AP][INFO] Starting server: server.jar
[22-07-2025 03:00:40][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[22-07-2025 03:00:40][AP][DEBUG][Server] process: Process[pid=260867, exitValue="not exited"]
[22-07-2025 03:00:40][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@170ee530
[22-07-2025 23:36:28][AP][INFO] Command '.stf' not found! Enter .help or .h for all available commands!


.stf

[22-07-2025 23:36:35][AP][INFO] Stopping server...
[22-07-2025 23:36:35][AP][DEBUG][Server] process: Process[pid=260867, exitValue="not exited"]
[22-07-2025 23:36:35][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@170ee530
[22-07-2025 23:36:35][AP][DEBUG][Server] Stopping server with command: "stop"
[22-07-2025 23:36:40][AP][INFO] Server was stopped.
[22-07-2025 23:36:40][AP][INFO] To stop AutoPlug too, enter '.stop both'.


.st

[22-07-2025 23:40:04][AP][INFO] Running pre-startup tasks, please be patient...
[22-07-2025 23:40:04][AP][INFO] [TASK] [\] > [SelfUpdater] Initialising...
[22-07-2025 23:40:04][AP][INFO] 
[22-07-2025 23:40:06][AP][DEBUG][TaskBackup] Excluded 'backups' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups
[22-07-2025 23:40:06][AP][DEBUG][TaskBackup] Excluded 'downloads' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads
[22-07-2025 23:40:06][AP][DEBUG][TaskBackup] Excluded 'system' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system
[22-07-2025 23:40:06][AP][DEBUG][TaskBackup] Excluded 'logs' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logs
[22-07-2025 23:40:06][AP][DEBUG][TaskBackup] Excluded 'dynmap' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/dynmap
[22-07-2025 23:40:06][AP][DEBUG][TaskBackup] Excluded 'WorldBorder' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/WorldBorder
[22-07-2025 23:40:09][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:09][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/cache/mojang_1.21.7.jar 0% - ADD_ENTRY
[22-07-2025 23:40:09][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[22-07-2025 23:40:09][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[22-07-2025 23:40:09][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[22-07-2025 23:40:09][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[22-07-2025 23:40:09][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[22-07-2025 23:40:09][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[22-07-2025 23:40:09][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[22-07-2025 23:40:09][AP][INFO] 
[22-07-2025 23:40:14][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:14][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/.paper-remapped/eco-6.76.2-all.jar 0% - ADD_ENTRY
[22-07-2025 23:40:14][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[22-07-2025 23:40:14][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[22-07-2025 23:40:14][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[22-07-2025 23:40:14][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[22-07-2025 23:40:14][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[22-07-2025 23:40:14][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[22-07-2025 23:40:14][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[22-07-2025 23:40:14][AP][INFO] 
[22-07-2025 23:40:19][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:19][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/libraries/org/joml/joml/1.10.5/joml-1.10.5.pom 0% - ADD_ENTRY
[22-07-2025 23:40:19][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[22-07-2025 23:40:19][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[22-07-2025 23:40:19][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[22-07-2025 23:40:19][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[22-07-2025 23:40:19][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[22-07-2025 23:40:19][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[22-07-2025 23:40:19][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[22-07-2025 23:40:19][AP][INFO] 
[22-07-2025 23:40:22][AP][DEBUG][TaskBackup] Created backup at: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups/2025-07-22-23.40-BACKUP.zip
[22-07-2025 23:40:23][AP][DEBUG][RestarterConfig] [03, 00]
[22-07-2025 23:40:23][AP][DEBUG][RestarterConfig] [03, 00]
[22-07-2025 23:40:23][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[22-07-2025 23:40:23][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[22-07-2025 23:40:23][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[22-07-2025 23:40:23][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[22-07-2025 23:40:23][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[22-07-2025 23:40:23][AP][DEBUG][UtilsMinecraft] 1.21.7
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[22-07-2025 23:40:23][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[22-07-2025 23:40:23][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[22-07-2025 23:40:23][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[22-07-2025 23:40:23][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[22-07-2025 23:40:23][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[22-07-2025 23:40:23][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[22-07-2025 23:40:23][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[22-07-2025 23:40:24][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:24][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 23:40:24][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 23:40:24][AP][INFO] [TASK] [|] > [DailyRestarter] Scheduler already running. Put into standby.
[22-07-2025 23:40:24][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 23:40:24][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 23:40:24][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 23:40:24][AP][INFO] [TASK] [/] > [PluginsUpdater] Checked 'RoseResourcepack' plugin (2/15)
[22-07-2025 23:40:24][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 23:40:24][AP][INFO] 
[22-07-2025 23:40:24][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[22-07-2025 23:40:24][AP][DEBUG][TaskPluginDownload] Downloading ServiceIO-[3.0.06].jar to '/run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads/ServiceIO-[3.0.06].jar' from 'https://cdn.modrinth.com/data/MNPyHOe7/versions/dIyizlw5/service-io-3.0.0-pre6-all.jar'
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[22-07-2025 23:40:24][AP][DEBUG][TaskPluginDownload] Installing plugin into /run/media/glitchy/Data/Projects/mc_server/src/plugins/ServiceIO-LATEST-[3.0.06].jar
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[22-07-2025 23:40:24][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[22-07-2025 23:40:25][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[22-07-2025 23:40:26][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[22-07-2025 23:40:26][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[22-07-2025 23:40:26][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[22-07-2025 23:40:26][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[22-07-2025 23:40:26][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[22-07-2025 23:40:27][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[22-07-2025 23:40:27][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[22-07-2025 23:40:27][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[22-07-2025 23:40:27][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[22-07-2025 23:40:28][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[22-07-2025 23:40:28][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[22-07-2025 23:40:28][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[22-07-2025 23:40:28][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 23:40:29][AP][INFO] [TASK] [-] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (13/15)
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 23:40:29][AP][INFO] [TASK] [#] > [PluginDownloader][100%] Installed update for ServiceIO successfully!
[22-07-2025 23:40:29][AP][INFO] 
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[22-07-2025 23:40:29][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[22-07-2025 23:40:30][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[22-07-2025 23:40:31][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 23:40:33][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 23:40:33][AP][INFO] [TASK] [#] > [PluginDownloader][100%] Installed update for ServiceIO successfully!
[22-07-2025 23:40:33][AP][INFO] 
[22-07-2025 23:40:33][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[22-07-2025 23:40:33][AP][INFO] [OK][BackupTask] Completed backup & skipped upload.
[22-07-2025 23:40:33][AP][INFO] [OK][GeneralTasks] Finished.
[22-07-2025 23:40:33][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[22-07-2025 23:40:33][AP][INFO] [OK][CustomRestarter] Skipped.
[22-07-2025 23:40:33][AP][INFO] [OK][JavaUpdater] Your Java installation is on the latest version!
[22-07-2025 23:40:33][AP][INFO] [OK][ServerUpdater] Up-to-date version
[22-07-2025 23:40:33][AP][INFO] [OK][PluginsUpdater] Checked 15/16 plugins. Some plugins were excluded.
[22-07-2025 23:40:33][AP][INFO] [OK][ModsUpdater] Skipped.
[22-07-2025 23:40:33][AP][INFO] [OK][PluginDownloader] Installed update for ServiceIO successfully!
[22-07-2025 23:40:33][AP][INFO] GeneralTasks:
[22-07-2025 23:40:33][AP][INFO] Directory cleaner removed 3 files, from directory ./autoplug/logs
[22-07-2025 23:40:33][AP][INFO] Directory cleaner removed 2 files, from directory ./logs
[22-07-2025 23:40:33][AP][INFO] PluginsUpdater:
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] Message: There was an api-error for RoseResourcepack!
[22-07-2025 23:40:33][AP][WARN] Details: Index 0 out of bounds for length 0
[22-07-2025 23:40:33][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[22-07-2025 23:40:33][AP][WARN] Stacktrace: 
[22-07-2025 23:40:33][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[22-07-2025 23:40:33][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[22-07-2025 23:40:33][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[22-07-2025 23:40:33][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[22-07-2025 23:40:33][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[22-07-2025 23:40:33][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] Message: null
[22-07-2025 23:40:33][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[22-07-2025 23:40:33][AP][WARN] Type: java.lang.Exception
[22-07-2025 23:40:33][AP][WARN] Stacktrace: 
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[22-07-2025 23:40:33][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] Message: null
[22-07-2025 23:40:33][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[22-07-2025 23:40:33][AP][WARN] Type: java.lang.Exception
[22-07-2025 23:40:33][AP][WARN] Stacktrace: 
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[22-07-2025 23:40:33][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][WARN] Message: null
[22-07-2025 23:40:33][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[22-07-2025 23:40:33][AP][WARN] Type: java.lang.Exception
[22-07-2025 23:40:33][AP][WARN] Stacktrace: 
[22-07-2025 23:40:33][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[22-07-2025 23:40:33][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[22-07-2025 23:40:33][AP][WARN] ================================
[22-07-2025 23:40:33][AP][INFO] Starting server: server.jar
[22-07-2025 23:40:33][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[22-07-2025 23:40:33][AP][DEBUG][Server] process: Process[pid=318635, exitValue="not exited"]
[22-07-2025 23:40:33][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@2c7ed000


.s

[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 23:40:34][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [PluginDownloader][100%] Installed update for ServiceIO successfully!
[22-07-2025 23:40:34][AP][INFO] 
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[22-07-2025 23:40:34][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[22-07-2025 23:40:34][AP][INFO] [TASK] [#] > [PluginDownloader][100%] Installed update for ServiceIO successfully!
[22-07-2025 23:40:34][AP][INFO] 
[23-07-2025 00:59:26][AP][INFO] Stopping server...
[23-07-2025 00:59:26][AP][DEBUG][Server] process: Process[pid=318635, exitValue="not exited"]
[23-07-2025 00:59:26][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@2c7ed000
[23-07-2025 00:59:26][AP][DEBUG][Server] Stopping server with command: "stop"


.st

[23-07-2025 00:59:32][AP][INFO] Server was stopped.
[23-07-2025 00:59:32][AP][INFO] To stop AutoPlug too, enter '.stop both'.
[23-07-2025 03:00:00][AP][WARN] ================================
[23-07-2025 03:00:00][AP][WARN] Message: Error while executing restart!
[23-07-2025 03:00:00][AP][WARN] Details: Server is not running. Restart not possible.
[23-07-2025 03:00:00][AP][WARN] Type: java.lang.Exception
[23-07-2025 03:00:00][AP][WARN] Stacktrace: 
[23-07-2025 03:00:00][AP][WARN] com.osiris.autoplug.client.tasks.scheduler.RestartJob.execute(RestartJob.java:31)
[23-07-2025 03:00:00][AP][WARN] org.quartz.core.JobRunShell.run(JobRunShell.java:202)
[23-07-2025 03:00:00][AP][WARN] org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
[23-07-2025 03:00:00][AP][WARN] ================================
[23-07-2025 11:28:24][AP][INFO] Running pre-startup tasks, please be patient...
[23-07-2025 11:28:24][AP][INFO] [TASK] [\] > [SelfUpdater] Initialising...
[23-07-2025 11:28:24][AP][INFO] [TASK] [|] > [BackupTask] Initialising...
[23-07-2025 11:28:24][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[23-07-2025 11:28:24][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[23-07-2025 11:28:24][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[23-07-2025 11:28:24][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[23-07-2025 11:28:24][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[23-07-2025 11:28:24][AP][INFO] 
[23-07-2025 11:28:26][AP][DEBUG][TaskBackup] Excluded 'backups' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups
[23-07-2025 11:28:26][AP][DEBUG][TaskBackup] Excluded 'downloads' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads
[23-07-2025 11:28:26][AP][DEBUG][TaskBackup] Excluded 'system' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system
[23-07-2025 11:28:26][AP][DEBUG][TaskBackup] Excluded 'logs' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logs
[23-07-2025 11:28:26][AP][DEBUG][TaskBackup] Excluded 'dynmap' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/dynmap
[23-07-2025 11:28:26][AP][DEBUG][TaskBackup] Excluded 'WorldBorder' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/WorldBorder
[23-07-2025 11:28:29][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 11:28:29][AP][INFO] [TASK] [-] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/LuckPerms/libs/asm-commons-9.8.jar 0% - ADD_ENTRY
[23-07-2025 11:28:29][AP][INFO] [TASK] [\] > [GeneralTasks] Initialising...
[23-07-2025 11:28:29][AP][INFO] [TASK] [|] > [DailyRestarter] Initialising...
[23-07-2025 11:28:29][AP][INFO] [TASK] [/] > [CustomRestarter] Initialising...
[23-07-2025 11:28:29][AP][INFO] [TASK] [-] > [JavaUpdater] Initialising...
[23-07-2025 11:28:29][AP][INFO] [TASK] [\] > [ServerUpdater] Initialising...
[23-07-2025 11:28:29][AP][INFO] [TASK] [|] > [PluginsUpdater] Initialising...
[23-07-2025 11:28:29][AP][INFO] [TASK] [/] > [ModsUpdater] Initialising...
[23-07-2025 11:28:29][AP][INFO] 
[23-07-2025 11:28:34][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 11:28:34][AP][INFO] [TASK] [-] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/logs/2025-07-18-1.log.gz 0% - ADD_ENTRY
[23-07-2025 11:28:34][AP][INFO] [TASK] [\] > [GeneralTasks] Initialising...
[23-07-2025 11:28:34][AP][INFO] [TASK] [|] > [DailyRestarter] Initialising...
[23-07-2025 11:28:34][AP][INFO] [TASK] [/] > [CustomRestarter] Initialising...
[23-07-2025 11:28:34][AP][INFO] [TASK] [-] > [JavaUpdater] Initialising...
[23-07-2025 11:28:34][AP][INFO] [TASK] [\] > [ServerUpdater] Initialising...
[23-07-2025 11:28:34][AP][INFO] [TASK] [|] > [PluginsUpdater] Initialising...
[23-07-2025 11:28:34][AP][INFO] [TASK] [/] > [ModsUpdater] Initialising...
[23-07-2025 11:28:34][AP][INFO] 
[23-07-2025 11:28:39][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 11:28:39][AP][INFO] [TASK] [-] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/versions/1.21.7/purpur-1.21.7.jar 0% - ADD_ENTRY
[23-07-2025 11:28:39][AP][INFO] [TASK] [\] > [GeneralTasks] Initialising...
[23-07-2025 11:28:39][AP][INFO] [TASK] [|] > [DailyRestarter] Initialising...
[23-07-2025 11:28:39][AP][INFO] [TASK] [/] > [CustomRestarter] Initialising...
[23-07-2025 11:28:39][AP][INFO] [TASK] [-] > [JavaUpdater] Initialising...
[23-07-2025 11:28:39][AP][INFO] [TASK] [\] > [ServerUpdater] Initialising...
[23-07-2025 11:28:39][AP][INFO] [TASK] [|] > [PluginsUpdater] Initialising...
[23-07-2025 11:28:39][AP][INFO] [TASK] [/] > [ModsUpdater] Initialising...
[23-07-2025 11:28:39][AP][INFO] 
[23-07-2025 11:28:40][AP][DEBUG][TaskBackup] Created backup at: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups/2025-07-23-11.28-BACKUP.zip
[23-07-2025 11:28:40][AP][DEBUG][RestarterConfig] [03, 00]
[23-07-2025 11:28:40][AP][DEBUG][RestarterConfig] [03, 00]
[23-07-2025 11:28:40][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[23-07-2025 11:28:40][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[23-07-2025 11:28:40][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[23-07-2025 11:28:40][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[23-07-2025 11:28:40][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[23-07-2025 11:28:40][AP][DEBUG][UtilsMinecraft] 1.21.7
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[23-07-2025 11:28:40][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[23-07-2025 11:28:40][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[23-07-2025 11:28:40][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[23-07-2025 11:28:41][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[23-07-2025 11:28:41][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[23-07-2025 11:28:41][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[23-07-2025 11:28:41][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[23-07-2025 11:28:41][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[23-07-2025 11:28:41][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[23-07-2025 11:28:42][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[23-07-2025 11:28:43][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[23-07-2025 11:28:44][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[23-07-2025 11:28:44][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[23-07-2025 11:28:44][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[23-07-2025 11:28:44][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 11:28:44][AP][INFO] [TASK] [-] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (13/15)
[23-07-2025 11:28:44][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 11:28:44][AP][INFO] 
[23-07-2025 11:28:45][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[23-07-2025 11:28:45][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[23-07-2025 11:28:45][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[23-07-2025 11:28:45][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[23-07-2025 11:28:45][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[23-07-2025 11:28:46][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[23-07-2025 11:28:47][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[23-07-2025 11:28:47][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[23-07-2025 11:28:47][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[23-07-2025 11:28:48][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[23-07-2025 11:28:49][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 11:28:49][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 11:28:49][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 11:28:49][AP][INFO] 
[23-07-2025 11:28:49][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[23-07-2025 11:28:49][AP][INFO] [OK][BackupTask] Completed backup & skipped upload.
[23-07-2025 11:28:49][AP][INFO] [OK][GeneralTasks] Finished.
[23-07-2025 11:28:49][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[23-07-2025 11:28:49][AP][INFO] [OK][CustomRestarter] Skipped.
[23-07-2025 11:28:49][AP][INFO] [OK][JavaUpdater] Your Java installation is on the latest version!
[23-07-2025 11:28:49][AP][INFO] [OK][ServerUpdater] Up-to-date version
[23-07-2025 11:28:49][AP][INFO] [OK][PluginsUpdater] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 11:28:49][AP][INFO] [OK][ModsUpdater] Skipped.
[23-07-2025 11:28:49][AP][INFO] GeneralTasks:
[23-07-2025 11:28:49][AP][INFO] Directory cleaner removed 2 files, from directory ./logs
[23-07-2025 11:28:49][AP][INFO] PluginsUpdater:
[23-07-2025 11:28:49][AP][WARN] ================================
[23-07-2025 11:28:49][AP][WARN] Message: There was an api-error for RoseResourcepack!
[23-07-2025 11:28:49][AP][WARN] Details: Index 0 out of bounds for length 0
[23-07-2025 11:28:49][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[23-07-2025 11:28:49][AP][WARN] Stacktrace: 
[23-07-2025 11:28:49][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[23-07-2025 11:28:49][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[23-07-2025 11:28:49][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[23-07-2025 11:28:49][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[23-07-2025 11:28:49][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[23-07-2025 11:28:49][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[23-07-2025 11:28:49][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[23-07-2025 11:28:49][AP][WARN] ================================
[23-07-2025 11:28:51][AP][WARN] ================================
[23-07-2025 11:28:51][AP][WARN] Message: null
[23-07-2025 11:28:51][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[23-07-2025 11:28:51][AP][WARN] Type: java.lang.Exception
[23-07-2025 11:28:51][AP][WARN] Stacktrace: 
[23-07-2025 11:28:51][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[23-07-2025 11:28:51][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[23-07-2025 11:28:51][AP][WARN] ================================
[23-07-2025 11:28:51][AP][WARN] ================================
[23-07-2025 11:28:51][AP][WARN] Message: null
[23-07-2025 11:28:51][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[23-07-2025 11:28:51][AP][WARN] Type: java.lang.Exception
[23-07-2025 11:28:51][AP][WARN] Stacktrace: 
[23-07-2025 11:28:51][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[23-07-2025 11:28:51][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[23-07-2025 11:28:51][AP][WARN] ================================
[23-07-2025 11:28:51][AP][WARN] ================================
[23-07-2025 11:28:51][AP][WARN] Message: null
[23-07-2025 11:28:51][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[23-07-2025 11:28:51][AP][WARN] Type: java.lang.Exception
[23-07-2025 11:28:51][AP][WARN] Stacktrace: 
[23-07-2025 11:28:51][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[23-07-2025 11:28:51][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[23-07-2025 11:28:51][AP][WARN] ================================
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 11:28:51][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 11:28:51][AP][INFO] 
[23-07-2025 11:28:51][AP][INFO] Starting server: server.jar
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 11:28:51][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 11:28:51][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 11:28:51][AP][INFO] 
[23-07-2025 11:28:51][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[23-07-2025 11:28:51][AP][DEBUG][Server] process: Process[pid=978263, exitValue="not exited"]
[23-07-2025 11:28:51][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@7afb8fc


.s

[23-07-2025 22:55:29][AP][INFO] Command '.R' not found! Enter .help or .h for all available commands!


.R

[23-07-2025 22:55:30][AP][INFO] Restarting server...
[23-07-2025 22:55:30][AP][INFO] Stopping server...
[23-07-2025 22:55:30][AP][DEBUG][Server] process: Process[pid=978263, exitValue="not exited"]
[23-07-2025 22:55:30][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@7afb8fc
[23-07-2025 22:55:30][AP][DEBUG][Server] Stopping server with command: "stop"
[23-07-2025 22:55:34][AP][INFO] Running pre-startup tasks, please be patient...
[23-07-2025 22:55:34][AP][INFO] [TASK] [\] > [SelfUpdater] Initialising...
[23-07-2025 22:55:34][AP][INFO] [TASK] [|] > [BackupTask] Initialising...
[23-07-2025 22:55:34][AP][INFO] 
[23-07-2025 22:55:35][AP][INFO] Server was stopped.
[23-07-2025 22:55:35][AP][INFO] To stop AutoPlug too, enter '.stop both'.
[23-07-2025 22:55:36][AP][DEBUG][TaskBackup] Excluded 'backups' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups
[23-07-2025 22:55:36][AP][DEBUG][TaskBackup] Excluded 'downloads' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads
[23-07-2025 22:55:36][AP][DEBUG][TaskBackup] Excluded 'system' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system
[23-07-2025 22:55:36][AP][DEBUG][TaskBackup] Excluded 'logs' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logs
[23-07-2025 22:55:36][AP][DEBUG][TaskBackup] Excluded 'dynmap' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/dynmap
[23-07-2025 22:55:36][AP][DEBUG][TaskBackup] Excluded 'WorldBorder' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/WorldBorder
[23-07-2025 22:55:39][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 22:55:39][AP][INFO] [TASK] [/] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/LuckPerms/libs/bytebuddy-1.15.11.jar 0% - ADD_ENTRY
[23-07-2025 22:55:39][AP][INFO] [TASK] [-] > [GeneralTasks] Initialising...
[23-07-2025 22:55:39][AP][INFO] [TASK] [\] > [DailyRestarter] Initialising...
[23-07-2025 22:55:39][AP][INFO] [TASK] [|] > [CustomRestarter] Initialising...
[23-07-2025 22:55:39][AP][INFO] [TASK] [/] > [JavaUpdater] Initialising...
[23-07-2025 22:55:39][AP][INFO] [TASK] [-] > [ServerUpdater] Initialising...
[23-07-2025 22:55:39][AP][INFO] [TASK] [\] > [PluginsUpdater] Initialising...
[23-07-2025 22:55:39][AP][INFO] [TASK] [|] > [ModsUpdater] Initialising...
[23-07-2025 22:55:39][AP][INFO] 
[23-07-2025 22:55:44][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 22:55:44][AP][INFO] [TASK] [/] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/world/entities/r.-1.-2.mca 0% - ADD_ENTRY
[23-07-2025 22:55:44][AP][INFO] [TASK] [-] > [GeneralTasks] Initialising...
[23-07-2025 22:55:44][AP][INFO] [TASK] [\] > [DailyRestarter] Initialising...
[23-07-2025 22:55:44][AP][INFO] [TASK] [|] > [CustomRestarter] Initialising...
[23-07-2025 22:55:44][AP][INFO] [TASK] [/] > [JavaUpdater] Initialising...
[23-07-2025 22:55:44][AP][INFO] [TASK] [-] > [ServerUpdater] Initialising...
[23-07-2025 22:55:44][AP][INFO] [TASK] [\] > [PluginsUpdater] Initialising...
[23-07-2025 22:55:44][AP][INFO] [TASK] [|] > [ModsUpdater] Initialising...
[23-07-2025 22:55:44][AP][INFO] 
[23-07-2025 22:55:49][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 22:55:49][AP][INFO] [TASK] [/] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/versions/1.21.7/purpur-1.21.7.jar 0% - ADD_ENTRY
[23-07-2025 22:55:49][AP][INFO] [TASK] [-] > [GeneralTasks] Initialising...
[23-07-2025 22:55:49][AP][INFO] [TASK] [\] > [DailyRestarter] Initialising...
[23-07-2025 22:55:49][AP][INFO] [TASK] [|] > [CustomRestarter] Initialising...
[23-07-2025 22:55:49][AP][INFO] [TASK] [/] > [JavaUpdater] Initialising...
[23-07-2025 22:55:49][AP][INFO] [TASK] [-] > [ServerUpdater] Initialising...
[23-07-2025 22:55:49][AP][INFO] [TASK] [\] > [PluginsUpdater] Initialising...
[23-07-2025 22:55:49][AP][INFO] [TASK] [|] > [ModsUpdater] Initialising...
[23-07-2025 22:55:49][AP][INFO] 
[23-07-2025 22:55:50][AP][DEBUG][TaskBackup] Created backup at: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups/2025-07-23-22.55-BACKUP.zip
[23-07-2025 22:55:50][AP][DEBUG][RestarterConfig] [03, 00]
[23-07-2025 22:55:50][AP][DEBUG][RestarterConfig] [03, 00]
[23-07-2025 22:55:50][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[23-07-2025 22:55:50][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[23-07-2025 22:55:50][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[23-07-2025 22:55:50][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[23-07-2025 22:55:50][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[23-07-2025 22:55:50][AP][DEBUG][UtilsMinecraft] 1.21.7
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[23-07-2025 22:55:50][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[23-07-2025 22:55:50][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[23-07-2025 22:55:50][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[23-07-2025 22:55:50][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[23-07-2025 22:55:51][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[23-07-2025 22:55:51][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[23-07-2025 22:55:51][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[23-07-2025 22:55:51][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[23-07-2025 22:55:52][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[23-07-2025 22:55:53][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[23-07-2025 22:55:54][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[23-07-2025 22:55:54][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 22:55:54][AP][INFO] [TASK] [/] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (13/15)
[23-07-2025 22:55:54][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 22:55:54][AP][INFO] 
[23-07-2025 22:55:54][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[23-07-2025 22:55:54][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[23-07-2025 22:55:55][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[23-07-2025 22:55:55][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[23-07-2025 22:55:55][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[23-07-2025 22:55:55][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[23-07-2025 22:55:56][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[23-07-2025 22:55:57][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[23-07-2025 22:55:57][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[23-07-2025 22:55:57][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[23-07-2025 22:55:57][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[23-07-2025 22:55:57][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[23-07-2025 22:55:57][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[23-07-2025 22:55:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 22:55:59][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 22:55:59][AP][INFO] 
[23-07-2025 22:55:59][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [OK][BackupTask] Completed backup & skipped upload.
[23-07-2025 22:55:59][AP][INFO] [OK][GeneralTasks] Finished.
[23-07-2025 22:55:59][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[23-07-2025 22:55:59][AP][INFO] [OK][CustomRestarter] Skipped.
[23-07-2025 22:55:59][AP][INFO] [OK][JavaUpdater] Your Java installation is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [OK][ServerUpdater] Up-to-date version
[23-07-2025 22:55:59][AP][INFO] [OK][PluginsUpdater] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 22:55:59][AP][INFO] [OK][ModsUpdater] Skipped.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 22:55:59][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 22:55:59][AP][INFO] 
[23-07-2025 22:55:59][AP][INFO] PluginsUpdater:
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] Message: There was an api-error for RoseResourcepack!
[23-07-2025 22:55:59][AP][WARN] Details: Index 0 out of bounds for length 0
[23-07-2025 22:55:59][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[23-07-2025 22:55:59][AP][WARN] Stacktrace: 
[23-07-2025 22:55:59][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[23-07-2025 22:55:59][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[23-07-2025 22:55:59][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[23-07-2025 22:55:59][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[23-07-2025 22:55:59][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[23-07-2025 22:55:59][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] Message: null
[23-07-2025 22:55:59][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[23-07-2025 22:55:59][AP][WARN] Type: java.lang.Exception
[23-07-2025 22:55:59][AP][WARN] Stacktrace: 
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[23-07-2025 22:55:59][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] Message: null
[23-07-2025 22:55:59][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[23-07-2025 22:55:59][AP][WARN] Type: java.lang.Exception
[23-07-2025 22:55:59][AP][WARN] Stacktrace: 
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[23-07-2025 22:55:59][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][WARN] Message: null
[23-07-2025 22:55:59][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[23-07-2025 22:55:59][AP][WARN] Type: java.lang.Exception
[23-07-2025 22:55:59][AP][WARN] Stacktrace: 
[23-07-2025 22:55:59][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[23-07-2025 22:55:59][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[23-07-2025 22:55:59][AP][WARN] ================================
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Your Java installation is on the latest version!
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[23-07-2025 22:55:59][AP][INFO] [TASK] [4] > [PluginsUpdater][100%] Checked 15/16 plugins. Some plugins were excluded.
[23-07-2025 22:55:59][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[23-07-2025 22:55:59][AP][INFO] 
[23-07-2025 22:55:59][AP][INFO] Starting server: server.jar
[23-07-2025 22:55:59][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[23-07-2025 22:55:59][AP][DEBUG][Server] process: Process[pid=1012799, exitValue="not exited"]
[23-07-2025 22:55:59][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@10c3db8e


.r

