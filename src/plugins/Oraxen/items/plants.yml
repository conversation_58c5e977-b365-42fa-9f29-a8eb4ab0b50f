# Material list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html
# EntityType list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/EntityType.html
# Attribute list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/attribute/Attribute.html
# Potion effects list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/potion/PotionEffectType.html
# Random UUID generator: https://www.uuidgenerator.net/
# Color codes: https://docs.adventure.kyori.net/minimessage.html#format

weed_leaf:
  itemname: <gradient:#46EEAA:#2CBFC7>Weed Leaf
  material: COOKED_BEEF
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/weed/leaf
  Mechanics:
    consumable_potion_effects:
      confusion:
        amplifier: 0
        ambient: true # Makes potion effect produce more, translucent, particles.
        particles: true # whether this effect has particles or not
        icon: true # whether this effect has an icon or not
        duration: 200 # 10 seconds in ticks
      speed:
        amplifier: 0
        ambient: true # Makes potion effect produce more, translucent, particles.
        particles: true # whether this effect has particles or not
        icon: true # whether this effect has an icon or not
        duration: 6000 # 5 minutes
      saturation:
        amplifier: 1
        ambient: true # Makes potion effect produce more, translucent, particles.
        particles: true # whether this effect has particles or not
        icon: true # whether this effect has an icon or not
        duration: 6000 # 5 minutes
      levitation:
        amplifier: 0
        ambient: true # Makes potion effect produce more, translucent, particles.
        particles: true # whether this effect has particles or not
        icon: true # whether this effect has an icon or not
        duration: 5 # 5 ticks

weed_seed:
  itemname: <gradient:#46EEAA:#2CBFC7>Weed Seed
  material: PAPER
  Mechanics:
    furniture:
      item: weed_plant_stage0
      barrier: false
      farmland_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
        next_stage: weed_plant_stage1
      drop:
        silktouch: true
        loots:
        - oraxen_item: weed_seed
          probability: 1.0
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/weed/seed

weed_plant_stage0:
  material: PAPER
  excludeFromInventory: true
  Pack:
    generate_model: false
    model: default/weed/stage0

weed_plant_stage1:
  material: PAPER
  excludeFromInventory: true
  Mechanics:
    furniture:
      barrier: false
      farmland_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
        next_stage: weed_plant_stage2
      drop:
        silktouch: true
        loots:
        - oraxen_item: weed_seed
          probability: 1.0
  Pack:
    generate_model: false
    model: default/weed/stage1

weed_plant_stage2:
  material: PAPER
  excludeFromInventory: true
  Mechanics:
    furniture:
      barrier: false
      farmland_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
        next_stage: weed_plant_stage3
      drop:
        silktouch: true
        loots:
        - oraxen_item: weed_seed
          probability: 1.0
  Pack:
    generate_model: false
    model: default/weed/stage2

weed_plant_stage3:
  material: PAPER
  excludeFromInventory: true
  Mechanics:
    furniture:
      barrier: false
      farmland_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
      drop:
        silktouch: true
        loots:
        - oraxen_item: weed_seed
          max_amount: 2
          probability: 0.75
        - oraxen_item: weed_leaf
          max_amount: 5
          probability: 0.55
  Pack:
    generate_model: false
    model: default/weed/stage3

grape:
  itemname: <gradient:#ED7B84:#9055FF>Grape
  material: COOKED_BEEF
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/grape

grape_seeds:
  itemname: <gradient:#46EEAA:#2CBFC7>Grape Seeds
  material: PAPER
  Mechanics:
    furniture:
      item: grape_stage0
      barrier: false
      farmblock_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
        next_stage: grape_stage1
      drop:
        silktouch: true
        loots:
        - oraxen_item: grape_seeds
          probability: 1.0
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/grape_seeds

grape_stage0:
  material: PAPER
  excludeFromInventory: true
  Pack:
    generate_model: false
    model: default/grape/stage0

grape_stage1:
  material: PAPER
  excludeFromInventory: true
  Mechanics:
    furniture:
      barrier: false
      farmblock_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
        next_stage: grape_stage2
      drop:
        silktouch: true
        loots:
        - oraxen_item: grape_seeds
          probability: 1.0
  Pack:
    generate_model: false
    model: default/grape/stage1

grape_stage2:
  material: PAPER
  excludeFromInventory: true
  Mechanics:
    furniture:
      barrier: false
      farmblock_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
        next_stage: grape_stage3
      drop:
        silktouch: true
        loots:
        - oraxen_item: grape_seeds
          probability: 1.0
  Pack:
    generate_model: false
    model: default/grape/stage2

grape_stage3:
  material: PAPER
  excludeFromInventory: true
  Mechanics:
    furniture:
      barrier: false
      farmblock_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
        next_stage: grape_stage4
      drop:
        silktouch: true
        loots:
        - oraxen_item: grape_seeds
          probability: 1.0
  Pack:
    generate_model: false
    model: default/grape/stage3

grape_stage4:
  material: PAPER
  excludeFromInventory: true
  Mechanics:
    furniture:
      barrier: false
      farmblock_required: true
      evolution:
        delay: 10000
        probability: 0.5
        light_boost: true
      drop:
        silktouch: true
        loots:
        - oraxen_item: grape_seeds
          max_amount: 2
          probability: 0.75
        - oraxen_item: grape
          max_amount: 2
          probability: 0.55
  Pack:
    generate_model: false
    model: default/grape/stage4
