# Material list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html
# EntityType list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/EntityType.html
# Attribute list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/attribute/Attribute.html
# Potion effects list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/potion/PotionEffectType.html
# Random UUID generator: https://www.uuidgenerator.net/
# Color codes: https://docs.adventure.kyori.net/minimessage.html#format

emerald_helmet:
  itemname: <gradient:#89E59D:#37C6BA>Emerald Helmet
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Gives 1 extra <glyph:heart>
  Components:
    max_stack_size: 1
    durability:
      value: 437
      damage_entity_hit: true
    equippable:
      slot: HEAD
      model: oraxen:emerald
  AttributeModifiers:
  - attribute: MAX_HEALTH
    amount: 2
    operation: 0
    slot: HEAD
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: HEAD
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: HEAD
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/emerald_helmet

emerald_chestplate:
  itemname: <gradient:#89E59D:#37C6BA>Emerald Chestplate
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Gives 1.5 extra <glyph:heart>
  Components:
    max_stack_size: 1
    durability:
      value: 635
      damage_entity_hit: true
    equippable:
      slot: CHEST
      model: oraxen:emerald
  AttributeModifiers:
  - attribute: MAX_HEALTH
    amount: 3
    operation: 0
    slot: CHEST
  - attribute: ARMOR
    amount: 8
    operation: 0
    slot: CHEST
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: CHEST
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/emerald_chestplate

emerald_leggings:
  itemname: <gradient:#89E59D:#37C6BA>Emerald Leggings
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Gives 1.5 extra <glyph:heart>
  Components:
    max_stack_size: 1
    durability:
      value: 595
      damage_entity_hit: true
    equippable:
      slot: LEGS
      model: oraxen:emerald
  AttributeModifiers:
  - attribute: MAX_HEALTH
    amount: 3
    operation: 0
    slot: LEGS
  - attribute: ARMOR
    amount: 6
    operation: 0
    slot: LEGS
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: LEGS
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/emerald_leggings

emerald_boots:
  itemname: <gradient:#89E59D:#37C6BA>Emerald Boots
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Gives 1 extra <glyph:heart>
  Components:
    max_stack_size: 1
    durability:
      value: 516
      damage_entity_hit: true
    equippable:
      slot: FEET
      model: oraxen:emerald
  AttributeModifiers:
  - attribute: MAX_HEALTH
    amount: 2
    operation: 0
    slot: FEET
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: FEET
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: FEET
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/emerald_boots

obsidian_helmet:
  itemname: <gradient:#4B36B1:#6699FF>Obsidian Helmet
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Ludicrous durability
  Components:
    max_stack_size: 1
    durability:
      value: 4370
      damage_entity_hit: true
    equippable:
      slot: HEAD
      model: oraxen:obsidian
  AttributeModifiers:
  - attribute: ARMOR
    amount: 2
    operation: 0
    slot: HEAD
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/obsidian_helmet

obsidian_chestplate:
  custom_model_data: 2
  itemname: <gradient:#4B36B1:#6699FF>Obsidian Chestplate
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Ludicrous durability
  Components:
    max_stack_size: 1
    durability:
      value: 6350
      damage_entity_hit: true
    equippable:
      slot: CHEST
      model: oraxen:obsidian
  AttributeModifiers:
  - attribute: ARMOR
    amount: 6
    operation: 0
    slot: CHEST
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/obsidian_chestplate

obsidian_leggings:
  itemname: <gradient:#4B36B1:#6699FF>Obsidian Leggings
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Ludicrous durability
  Components:
    max_stack_size: 1
    durability:
      value: 5950
      damage_entity_hit: true
    equippable:
      slot: LEGS
      model: oraxen:obsidian
  AttributeModifiers:
  - attribute: ARMOR
    amount: 5
    operation: 0
    slot: LEGS
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/obsidian_leggings

obsidian_boots:
  itemname: <gradient:#4B36B1:#6699FF>Boots Boots
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>Ludicrous durability
  Components:
    max_stack_size: 1
    durability:
      value: 5160
      damage_entity_hit: true
    equippable:
      slot: FEET
      model: oraxen:obsidian
  AttributeModifiers:
  - attribute: ARMOR
    amount: 2
    operation: 0
    slot: FEET
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/obsidian_boots

ruby_helmet:
  itemname: <gradient:#FA7CBB:#F14658>Ruby Helmet
  material: PAPER
  Components:
    max_stack_size: 1
    durability:
      value: 547
      damage_entity_hit: true
    equippable:
      slot: HEAD
      model: oraxen:ruby
  AttributeModifiers:
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: HEAD
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: HEAD
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/ruby_helmet

ruby_chestplate:
  itemname: <gradient:#FA7CBB:#F14658>Ruby Chestplate
  material: PAPER
  Components:
    max_stack_size: 1
    durability:
      value: 792
      damage_entity_hit: true
    equippable:
      slot: CHEST
      model: oraxen:ruby
  AttributeModifiers:
  - attribute: ARMOR
    amount: 8
    operation: 0
    slot: CHEST
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: CHEST
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/ruby_chestplate

ruby_leggings:
  itemname: <gradient:#FA7CBB:#F14658>Ruby Leggings
  material: PAPER
  Components:
    max_stack_size: 1
    durability:
      value: 742
      damage_entity_hit: true
    equippable:
      slot: LEGS
      model: oraxen:ruby
  AttributeModifiers:
  - attribute: ARMOR
    amount: 6
    operation: 0
    slot: LEGS
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: LEGS
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/ruby_leggings

ruby_boots:
  itemname: <gradient:#FA7CBB:#F14658>Ruby Boots
  material: PAPER
  Components:
    max_stack_size: 1
    durability:
      value: 643
      damage_entity_hit: true
    equippable:
      slot: FEET
      model: oraxen:ruby
  AttributeModifiers:
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: FEET
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: FEET
  Pack:
    generate_model: true
    parent_model: item/generated
    textures:
    - default/armors/ruby_boots
