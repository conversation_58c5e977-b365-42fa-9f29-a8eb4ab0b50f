[19-07-2025 16:42:37][AP][DEBUG][AL] Started Logger(AP)
[19-07-2025 16:42:37][AP][DEBUG][Main] !!!IMPORTANT!!! -> THIS LOG-FILE CONTAINS SENSITIVE INFORMATION <- !!!IMPORTANT!!!
[19-07-2025 16:42:37][AP][DEBUG][Main] !!!IMPORTANT!!! -> THIS LOG-FILE CONTAINS SENSITIVE INFORMATION <- !!!IMPORTANT!!!
[19-07-2025 16:42:37][AP][DEBUG][Main] !!!IMPORTANT!!! -> THIS LOG-FILE CONTAINS SENSITIVE INFORMATION <- !!!IMPORTANT!!!
[19-07-2025 16:42:37][AP][DEBUG][Main] START COMMAND: nulljava -jar AutoPlug-Client.jar 
[19-07-2025 16:42:37][AP][DEBUG][Main] JAR: /run/media/glitchy/Data/Projects/mc_server/src/AutoPlug-Client.jar
[19-07-2025 16:42:37][AP][DEBUG][Main] ARGS: []
[19-07-2025 16:42:37][AP][DEBUG][Main] SYSTEM OS: Linux
[19-07-2025 16:42:37][AP][DEBUG][Main] SYSTEM OS ARCH: amd64
[19-07-2025 16:42:37][AP][DEBUG][Main] SYSTEM VERSION: 6.15.6-zen1-1-zen
[19-07-2025 16:42:37][AP][DEBUG][Main] JAVA VERSION: 21.0.8
[19-07-2025 16:42:37][AP][DEBUG][Main] JAVA VENDOR: Arch Linux https://openjdk.org/
[19-07-2025 16:42:37][AP][DEBUG][Main] JAVA DIR: /usr/lib/jvm/java-21-openjdk
[19-07-2025 16:42:37][AP][DEBUG][Main] WORKING DIR: /run/media/glitchy/Data/Projects/mc_server/src
[19-07-2025 16:42:37][AP][DEBUG][Main] TEST-MODE: false
[19-07-2025 16:42:37][AP][INFO] | ------------------------------------------- |
[19-07-2025 16:42:37][AP][INFO]      ___       __       ___  __             
[19-07-2025 16:42:37][AP][INFO]     / _ |__ __/ /____  / _ \/ /_ _____ _   
[19-07-2025 16:42:37][AP][INFO]    / __ / // / __/ _ \/ ___/ / // / _ `/   
[19-07-2025 16:42:37][AP][INFO]   /_/ |_\_,_/\__/\___/_/  /_/\_,_/\_, /
[19-07-2025 16:42:37][AP][INFO]                                  /___/    
[19-07-2025 16:42:37][AP][INFO] AutoPlug-Client 8.3.1 by Osiris-Team
[19-07-2025 16:42:37][AP][INFO] Web-Panel: https://autoplug.one/
[19-07-2025 16:42:37][AP][INFO] | ------------------------------------------- |
[19-07-2025 16:42:37][AP][DEBUG][GeneralConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/general.yml
[19-07-2025 16:42:37][AP][DEBUG][UpdaterConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/updater.yml
[19-07-2025 16:42:37][AP][DEBUG][LoggerConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logger.yml
[19-07-2025 16:42:37][AP][DEBUG][WebConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/web.yml
[19-07-2025 16:42:37][AP][DEBUG][BackupConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backup.yml
[19-07-2025 16:42:37][AP][DEBUG][RestarterConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/restarter.yml
[19-07-2025 16:42:37][AP][DEBUG][RestarterConfig] [03, 00]
[19-07-2025 16:42:37][AP][DEBUG][RestarterConfig] [03, 00]
[19-07-2025 16:42:37][AP][DEBUG][SharedFilesConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/shared-files.yml
[19-07-2025 16:42:37][AP][DEBUG][SSHConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/ssh.yml
[19-07-2025 16:42:37][AP][DEBUG][PluginsConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/plugins.yml
[19-07-2025 16:42:37][AP][DEBUG][ModsConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/mods.yml
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, autoplug] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, autoplug, auto-stop] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, autoplug, start-on-boot] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, autoplug, target-software] VAL: [MINECRAFT_SERVER] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, autoplug, system-tray, enable] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, autoplug, system-tray, theme] VAL: [darcula] DEF: [light]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server, key] VAL: NOT SHOWN DUE TO SECURITY RISK  DEF: [INSERT_KEY_HERE]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server, version] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server, auto-start] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server, auto-eula] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server, start-command] VAL: [java -Xms6144M -Xmx6144M -jar "server.jar" nogui] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server, stop-command] VAL: [stop] DEF: [stop, shutdown, end, close, finish, terminate, abort]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, server, restart-on-crash] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, directory-cleaner] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, directory-cleaner, enabled] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, directory-cleaner, max-days] VAL: [7] DEF: [7]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [general, directory-cleaner, list] VAL: [true ./autoplug/logs, ./autoplug/downloads, ./logs] DEF: [true ./autoplug/logs, ./autoplug/downloads]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, debug] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, autoplug-label] VAL: [AP] DEF: [AP]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, force-ANSI] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, color-server-log] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, tasks] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, tasks, live-tasks, enable] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, tasks, live-tasks, refresh-interval] VAL: [500] DEF: [500]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, tasks, show-warnings] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [logger, tasks, show-detailed-warnings] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, online-console] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, online-system-console] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, updater-results, send-plugins-updaters-results] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, updater-results, send-server-updaters-results] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, updater-results, send-self-updaters-results] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, send-details, public] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, send-details, private] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, send-details, ip] VAL: [127.0.0.1] DEF: [127.0.0.1]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, send-details, port] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [web, file-manager] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, max-days] VAL: [7] DEF: [7]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, cool-down] VAL: [500] DEF: [500]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, path] VAL: [./autoplug/backups] DEF: [./autoplug/backups]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, include, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, include, list] VAL: [./, ./server.properties] DEF: [./, ./server.properties]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, exclude, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, exclude, list] VAL: [./autoplug/backups, ./autoplug/downloads, ./autoplug/system, ./autoplug/logs, ./plugins/dynmap, ./plugins/WorldBorder] DEF: [./autoplug/backups, ./autoplug/downloads, ./autoplug/system, ./autoplug/logs, ./plugins/dynmap, ./plugins/WorldBorder]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, enable] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, delete-on-complete] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, host] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, port] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, username] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, password] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, path] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [backup, upload, rsa-key-path] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, times] VAL: [03:00] DEF: [05:00]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 10] VAL: [say Restarting in 10 seconds., say Please allow up to 2min for this process to complete.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 3] VAL: [say Restarting in 3.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 2] VAL: [say Restarting in 2.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 1] VAL: [say Restarting in 1.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, daily-restarter, commands, list, 0] VAL: [say Restarting...] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, enable] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, cron] VAL: [0 30 9 * * ? *] DEF: [0 30 9 * * ? *]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 10] VAL: [say Restarting in 10 seconds., say Please allow up to 2min for this process to complete.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 3] VAL: [say Restarting in 3.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 2] VAL: [say Restarting in 2.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 1] VAL: [say Restarting in 1.] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [restarter, custom-restarter, commands, list, 0] VAL: [say Restarting...] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, global-cool-down] VAL: [60] DEF: [60]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, global-recurring-checks] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, global-recurring-checks, enable] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, global-recurring-checks, intervall] VAL: [12] DEF: [12]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, self-updater] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, self-updater, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, self-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, self-updater, build] VAL: [stable] DEF: [stable]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, java-updater] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, java-updater, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, java-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, java-updater, version] VAL: [21] DEF: [21]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, java-updater, build-id] VAL: [6] DEF: [0]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, java-updater, large-heap] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, software] VAL: [purpur] DEF: [paper]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, steam-cmd-login] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, version] VAL: [1.21.7] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, build-id] VAL: [0] DEF: [0]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, skip-hash-check] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, github, repo-name] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, github, asset-name] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, github, version] VAL: [0] DEF: [0]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, jenkins, project-url] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, jenkins, artifact-name] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, server-updater, alternatives, jenkins, build-id] VAL: [0] DEF: [0]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater, enable] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater, path] VAL: [./plugins] DEF: [./plugins]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater, version] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater, async] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater, web-database, enable] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, plugins-updater, web-database, min-usages] VAL: [50] DEF: [50]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, mods-updater] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, mods-updater, enable] VAL: [false] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, mods-updater, profile] VAL: [AUTOMATIC] DEF: [AUTOMATIC]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, mods-updater, path] VAL: [./mods] DEF: [./mods]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, mods-updater, version] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, mods-updater, async] VAL: [true] DEF: [true]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [updater, mods-updater, check-name-for-mod-loader] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [shared-files] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [shared-files, enable] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [shared-files, copy-from] VAL: [./plugins, ./server.jar] DEF: [./plugins, ./server.jar]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [shared-files, send-to] VAL: [C:\User\Peter\servers\my-second-server, ./servers/another-server] DEF: [C:\User\Peter\servers\my-second-server, ./servers/another-server]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh, enabled] VAL: [false] DEF: [false]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh, port] VAL: [22] DEF: [22]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh, auth-method] VAL: [key-only] DEF: [key-only]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh, allowed-keys-path] VAL: [./autoplug/allowed_ssh_keys.txt] DEF: [./autoplug/allowed_ssh_keys.txt]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh, server-private-key] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh, username] VAL: [autoplug] DEF: [autoplug]
[19-07-2025 16:42:37][AP][DEBUG][UtilsConfig] [ssh, password] VAL: [] DEF: []
[19-07-2025 16:42:37][AP][INFO] Checked configs, took 166ms
[19-07-2025 16:42:37][AP][DEBUG][SystemConfig] Listening for changes for /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/config.yml
[19-07-2025 16:42:37][AP][INFO] Authenticating server...
[19-07-2025 16:42:37][AP][DEBUG][ConMain] open()
[19-07-2025 16:42:37][AP][DEBUG][ConMain] _close()
[19-07-2025 16:42:37][AP][WARN] ================================
[19-07-2025 16:42:37][AP][WARN] Message: null
[19-07-2025 16:42:37][AP][WARN] Details: No valid key provided. Register your server at https://autoplug.one/, get your server-key and add it to the /autoplug/general.yml config file. Enter '.con reload' to retry.
[19-07-2025 16:42:37][AP][WARN] Type: java.security.InvalidKeyException
[19-07-2025 16:42:37][AP][WARN] Stacktrace: 
[19-07-2025 16:42:37][AP][WARN] com.osiris.autoplug.client.network.online.DefaultConnection._open(DefaultConnection.java:123)
[19-07-2025 16:42:37][AP][WARN] com.osiris.autoplug.client.network.online.DefaultConnection.open(DefaultConnection.java:104)
[19-07-2025 16:42:37][AP][WARN] com.osiris.autoplug.client.network.online.ConMain.open(ConMain.java:49)
[19-07-2025 16:42:37][AP][WARN] com.osiris.autoplug.client.Main.main(Main.java:300)
[19-07-2025 16:42:37][AP][WARN] ================================
[19-07-2025 16:42:37][AP][INFO] Initialised successfully.
[19-07-2025 16:42:37][AP][INFO] | ------------------------------------------- |
[19-07-2025 16:42:37][AP][INFO] Enter .help for a list of all commands.
[19-07-2025 16:42:37][AP][DEBUG][ConPluginCommandReceive] Binding on localhost:35565 for AutoPlug-Plugin...
[19-07-2025 16:42:37][AP][DEBUG][ConPluginCommandReceive] Success!
[19-07-2025 16:42:37][AP][DEBUG][ConPluginCommandReceive] Waiting for AutoPlug-Plugin to connect...
[19-07-2025 16:42:37][AP][INFO] Running pre-startup tasks, please be patient...
[19-07-2025 16:42:38][AP][INFO] [TASK] [\] > [SelfUpdater] Initialising...
[19-07-2025 16:42:38][AP][INFO] 
[19-07-2025 16:42:40][AP][DEBUG][TaskBackup] Excluded 'backups' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups
[19-07-2025 16:42:40][AP][DEBUG][TaskBackup] Excluded 'downloads' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/downloads
[19-07-2025 16:42:40][AP][DEBUG][TaskBackup] Excluded 'system' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/system
[19-07-2025 16:42:40][AP][DEBUG][TaskBackup] Excluded 'logs' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/logs
[19-07-2025 16:42:40][AP][DEBUG][TaskBackup] Excluded 'dynmap' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/dynmap
[19-07-2025 16:42:40][AP][DEBUG][TaskBackup] Excluded 'WorldBorder' from backup. Full path: /run/media/glitchy/Data/Projects/mc_server/src/plugins/WorldBorder
[19-07-2025 16:42:43][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[19-07-2025 16:42:43][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/plugins/CustomCrops/libs/zstd-jni-1.5.6-9.jar 0% - ADD_ENTRY
[19-07-2025 16:42:43][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[19-07-2025 16:42:43][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[19-07-2025 16:42:43][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[19-07-2025 16:42:43][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[19-07-2025 16:42:43][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[19-07-2025 16:42:43][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[19-07-2025 16:42:43][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[19-07-2025 16:42:43][AP][INFO] 
[19-07-2025 16:42:48][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[19-07-2025 16:42:48][AP][INFO] [TASK] [|] > [BackupTask] Backing up /run/media/glitchy/Data/Projects/mc_server/src/server.jar 0% - ADD_ENTRY
[19-07-2025 16:42:48][AP][INFO] [TASK] [/] > [GeneralTasks] Initialising...
[19-07-2025 16:42:48][AP][INFO] [TASK] [-] > [DailyRestarter] Initialising...
[19-07-2025 16:42:48][AP][INFO] [TASK] [\] > [CustomRestarter] Initialising...
[19-07-2025 16:42:48][AP][INFO] [TASK] [|] > [JavaUpdater] Initialising...
[19-07-2025 16:42:48][AP][INFO] [TASK] [/] > [ServerUpdater] Initialising...
[19-07-2025 16:42:48][AP][INFO] [TASK] [-] > [PluginsUpdater] Initialising...
[19-07-2025 16:42:48][AP][INFO] [TASK] [\] > [ModsUpdater] Initialising...
[19-07-2025 16:42:48][AP][INFO] 
[19-07-2025 16:42:51][AP][DEBUG][TaskBackup] Created backup at: /run/media/glitchy/Data/Projects/mc_server/src/autoplug/backups/2025-07-19-16.42-BACKUP.zip
[19-07-2025 16:42:52][AP][DEBUG][RestarterConfig] [03, 00]
[19-07-2025 16:42:52][AP][DEBUG][RestarterConfig] [03, 00]
[19-07-2025 16:42:52][AP][DEBUG][TaskJavaUpdater] Determined 'AMD64' as operating systems architecture.
[19-07-2025 16:42:52][AP][DEBUG][TaskJavaUpdater] Determined 'LINUX' as operating system.
[19-07-2025 16:42:52][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=50&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[19-07-2025 16:42:52][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting latest build from https://api.purpurmc.org/v2/purpur/1.21.7/
[19-07-2025 16:42:52][AP][DEBUG][UtilsMinecraft] /run/media/glitchy/Data/Projects/mc_server/src/server.jar
[19-07-2025 16:42:52][AP][DEBUG][UtilsMinecraft] 1.21.7
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching latest release... (https://api.spiget.org/v2/resources/1997/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchByName] [Actions] Searching for plugin Actions(Auxilor)...
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching latest release... (https://api.spiget.org/v2/resources/6245/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching latest release... (https://api.spiget.org/v2/resources/74225/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching latest release... (https://api.spiget.org/v2/resources/86576/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching latest release... (https://api.spiget.org/v2/resources/117785/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching latest release... (https://api.spiget.org/v2/resources/79573/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching latest release... (https://api.spiget.org/v2/resources/88246/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MNPyHOe7/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching latest release... (https://api.spiget.org/v2/resources/28140/versions?size=1&sort=-releaseDate)
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchByName] [CustomCrops] Searching for plugin CustomCrops(XiaoMoMi)...
[19-07-2025 16:42:52][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/cLBscQQD/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[19-07-2025 16:42:52][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[19-07-2025 16:42:52][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/MubyTbnA/version?loaders=[%22spigot%22,%22paper%22]&game_versions=[%221.21.7%22]
[19-07-2025 16:42:52][AP][DEBUG][SpigotSearchByName] [eco] Searching for plugin eco(Auxilor)...
[19-07-2025 16:42:52][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/assets/version/%28%2C21.0.8%2B9.0%5D?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&lts=true&os=linux&page=0&page_size=20&project=jdk&release_type=ga&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse
[19-07-2025 16:42:52][AP][DEBUG][TaskServerUpdater] [DEBUG] Getting checksum from https://api.purpurmc.org/v2/purpur/1.21.7/2477/
[19-07-2025 16:42:52][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/zS3HK0Z8/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[19-07-2025 16:42:52][AP][DEBUG][ModrinthAPI] https://api.modrinth.com/v2/project/7FAiLjDP/version?loaders=["spigot","paper"]&game_versions=["1.21.7"]
[19-07-2025 16:42:52][AP][DEBUG][AdoptV3API] https://api.adoptium.net/v3/binary/version/jdk-21.0.7+6/linux/x64/jdk/hotspot/normal/eclipse?project=jdk
[19-07-2025 16:42:52][AP][DEBUG][TaskJavaUpdater] Update found 6 -> 9
[19-07-2025 16:42:52][AP][DEBUG][TaskJavaDownload] Downloading JDK-21.0.8+9.0.LTS.file from: https://api.adoptium.net/v3/binary/version/jdk-21.0.7+6/linux/x64/jdk/hotspot/normal/eclipse?project=jdk
[19-07-2025 16:42:52][AP][DEBUG][TaskServerUpdater] [DEBUG] Checksum: 366994c6203cf00936255a2f3ddaafa4
[19-07-2025 16:42:52][AP][DEBUG][TaskServerUpdater] [DEBUG] Current checksum: 366994c6203cf00936255a2f3ddaafa4
[19-07-2025 16:42:53][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[19-07-2025 16:42:53][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[19-07-2025 16:42:53][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[19-07-2025 16:42:53][AP][INFO] [TASK] [|] > [DailyRestarter] Initialising...
[19-07-2025 16:42:53][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[19-07-2025 16:42:53][AP][INFO] [TASK] [/] > [JavaUpdater] Update found (6 -> 9), started download!
[19-07-2025 16:42:53][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[19-07-2025 16:42:53][AP][INFO] [TASK] [-] > [PluginsUpdater] Checked 'FreedomChat' plugin (2/16)
[19-07-2025 16:42:53][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[19-07-2025 16:42:53][AP][INFO] [TASK] [\] > [JavaDownloader] Downloading JDK-21.0.8+9.0.LTS.file... (0mb/0mb)
[19-07-2025 16:42:53][AP][INFO] 
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [ProtocolLib] Fetching resource details... (https://api.spiget.org/v2/resources/1997)
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [EcoEnchants] Fetching resource details... (https://api.spiget.org/v2/resources/79573)
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [EcoItems] Fetching resource details... (https://api.spiget.org/v2/resources/117785)
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [EcoMobs] Fetching resource details... (https://api.spiget.org/v2/resources/86576)
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [EcoArmor] Fetching resource details... (https://api.spiget.org/v2/resources/88246)
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Fetching resource details... (https://api.spiget.org/v2/resources/6245)
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Fetching resource details... (https://api.spiget.org/v2/resources/74225)
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [LuckPerms] Fetching resource details... (https://api.spiget.org/v2/resources/28140)
[19-07-2025 16:42:53][AP][DEBUG][TaskDailyRestarter] Creating job with name: restartJob0 trigger:restartTrigger0 min:0 hour:3
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchByName] [CustomCrops] Found 0 similar plugins!
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchByName] [CustomCrops] No match found for CustomCrops!
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Searching for author XiaoMoMi(CustomCrops)...
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchByName] [eco] Found 10 similar plugins!
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchByName] [Actions] Found 10 similar plugins!
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchById] [ProtocolLib] Finished check with results: code:UP_TO_DATE latest:5.3.0 downloadURL:https://api.spiget.org/v2/resources/1997/download type:external 
[19-07-2025 16:42:53][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Gypopo is: 0.14285714285714285
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchById] [EcoMobs] Finished check with results: code:UP_TO_DATE latest:10.21.1 downloadURL:https://api.spiget.org/v2/resources/86576/download type:.jar 
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchById] [EcoEnchants] Finished check with results: code:UP_TO_DATE latest:12.23.0 downloadURL:https://api.spiget.org/v2/resources/79573/download type:.jar 
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 1 similar authors...
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> XiaoMoMi and XiaoMoMi is: 1.0
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Author matches! Continuing with XiaoMoMi ID: 1070823
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Cayorion is: 0.25
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchById] [LuckPerms] Finished check with results: code:UP_TO_DATE latest:5.5.0 downloadURL:https://api.spiget.org/v2/resources/28140/download type:.jar 
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchById] [EcoArmor] Finished check with results: code:UP_TO_DATE latest:8.75.1 downloadURL:https://api.spiget.org/v2/resources/88246/download type:.jar 
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchById] [AutoPluginLoader] Finished check with results: code:UP_TO_DATE latest:1.5.1 downloadURL:https://api.spiget.org/v2/resources/74225/download type:.jar 
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchById] [PlaceholderAPI] Finished check with results: code:UP_TO_DATE latest:2.11.6 downloadURL:https://api.spiget.org/v2/resources/6245/download type:.jar 
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchById] [EcoItems] Finished check with results: code:UP_TO_DATE latest:5.57.6 downloadURL:https://api.spiget.org/v2/resources/117785/download type:.jar 
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and antonschouten is: 0.15384615384615385
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ajneb is: 0.14285714285714285
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cayorion is: 0.25
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Found 3 resources of this author...
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and DirectionalBlock [ItemsAdder Addon] is: 0.14285714285714285
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and Shipping Bin is: 0.0
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] Similarity between -> CustomCrops and MiniBossBar is: 0.0
[19-07-2025 16:42:54][AP][DEBUG][SpigotSearchByAuthor] [CustomCrops] No match found for CustomCrops!
[19-07-2025 16:42:55][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Driftay is: 0.0
[19-07-2025 16:42:55][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and rc is: 0.0
[19-07-2025 16:42:55][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[19-07-2025 16:42:56][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Elikill is: 0.14285714285714285
[19-07-2025 16:42:56][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Ullrimax is: 0.125
[19-07-2025 16:42:56][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Catch is: 0.0
[19-07-2025 16:42:56][AP][DEBUG][TaskJavaDownload] Comparing hashes (SHA-256):
[19-07-2025 16:42:56][AP][DEBUG][TaskJavaDownload] Input-Hash: 974d3acef0b7193f541acb61b76e81670890551366625d4f6ca01b91ac152ce0
[19-07-2025 16:42:56][AP][DEBUG][TaskJavaDownload] File-Hash: 974d3acef0b7193f541acb61b76e81670890551366625d4f6ca01b91ac152ce0
[19-07-2025 16:42:56][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and novucs is: 0.0
[19-07-2025 16:42:57][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Cerexus is: 0.0
[19-07-2025 16:42:57][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and ProSavage is: 0.0
[19-07-2025 16:42:57][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Devmart is: 0.0
[19-07-2025 16:42:57][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and DanTheTechMan is: 0.07692307692307693
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[19-07-2025 16:42:58][AP][INFO] [TASK] [|] > [JavaUpdater] Java update downloaded. Removing old installation...
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[19-07-2025 16:42:58][AP][INFO] [TASK] [/] > [PluginsUpdater] Checked 'AutoPluginLoader' plugin (14/16)
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[19-07-2025 16:42:58][AP][INFO] [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)
[19-07-2025 16:42:58][AP][INFO] 
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and Whoktor is: 0.2857142857142857
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and TheTealViper is: 0.25
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByName] [eco] Similarity between -> Auxilor and creatorfromhell is: 0.13333333333333333
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByName] [eco] No match found for eco!
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByAuthor] [eco] Searching for author Auxilor(eco)...
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByName] [Actions] Similarity between -> Auxilor and Valetorev is: 0.2222222222222222
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByName] [Actions] No match found for Actions!
[19-07-2025 16:42:58][AP][DEBUG][SpigotSearchByAuthor] [Actions] Searching for author Auxilor(Actions)...
[19-07-2025 16:42:59][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 1 similar authors...
[19-07-2025 16:42:59][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> Auxilor and Auxilor is: 1.0
[19-07-2025 16:42:59][AP][DEBUG][SpigotSearchByAuthor] [eco] Author matches! Continuing with Auxilor ID: 507394
[19-07-2025 16:42:59][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 1 similar authors...
[19-07-2025 16:42:59][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Auxilor and Auxilor is: 1.0
[19-07-2025 16:42:59][AP][DEBUG][SpigotSearchByAuthor] [Actions] Author matches! Continuing with Auxilor ID: 507394
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Found 30 resources of this author...
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.04838709677419355
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.037037037037037035
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.031914893617021274
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems [FREE] ⭕ Make Custom Items! is: 0.08333333333333333
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.04285714285714286
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and RarityDisplay is: 0.0
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.04225352112676056
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.034482758620689655
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.03529411764705882
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.03614457831325301
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Sprint Artifacts (EcoEnchants Extension) is: 0.075
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.034482758620689655
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.03614457831325301
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.038461538461538464
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.03571428571428571
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.03614457831325301
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.03488372093023256
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Transmission is: 0.08333333333333333
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.03488372093023256
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Firewand is: 0.09375
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Citizen is: 0.0967741935483871
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Rainbow is: 0.0967741935483871
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and SimpleNightVision is: 0.11764705882352941
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoEnchants Extension ⚡ Intimidation is: 0.08333333333333333
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and JoinAndLeave is: 0.08333333333333333
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.03488372093023256
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.0379746835443038
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.034482758620689655
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.034482758620689655
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] Similarity between -> eco and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.03529411764705882
[19-07-2025 16:43:00][AP][DEBUG][SpigotSearchByAuthor] [eco] No match found for eco!
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Found 30 resources of this author...
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EnchantmentNumbers - High Level Enchantment Numbers Formatting is: 0.08064516129032258
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants ⭕ 250+ Enchantments ✅ Create Custom Enchants ✨ Essentials/CMI Support is: 0.07407407407407407
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions [FREE] ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.07446808510638298
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems [FREE] ⭕ Make Custom Items! is: 0.1388888888888889
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SpawnRateControl - Nerf the natural spawn rate of any mob in the game! is: 0.08571428571428572
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and RarityDisplay is: 0.23076923076923078
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and CraftableNotch - Bring back the Enchanted Golden Apple crafting recipe! is: 0.07042253521126761
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoSkills ⭕ Addictive MMORPG Skills ✅ Create Skills, Stats, Effects, Mana ✨ Plug + Play is: 0.06896551724137931
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Talismans ⭕ Create Custom Talismans ✅ Powerful Passive Effects ✨ Talisman Bag, Levels is: 0.08235294117647059
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoArmor ⭕ Create Custom Armor ✅ Premade Sets ✨ Upgrades, Crafting, Custom Textures is: 0.07228915662650602
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Sprint Artifacts (EcoEnchants Extension) is: 0.15
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoItems ⭕ Create Custom Items ✅ Weapons, Armors, Tools, Charms ✨ Item Levels, Rarities is: 0.08045977011494253
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoMobs ⭕ Create Custom Mobs ✅ Custom Mob AI ✨ Natural Spawns, Custom Model Support is: 0.07228915662650602
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoJobs ⭕ Powerful Employment System ✅ Create custom jobs ✨ Levelling, Effects is: 0.07692307692307693
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Reforges ⭕ Create Custom Reforges ✅ Item Modfiers ✨ Reforge Stones, GUI, NPC Support is: 0.08333333333333333
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoPets ⭕ Create Custom Pets ✅ Premade Pets ✨ Levelling, Abilities, Custom Textures is: 0.07228915662650602
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Boosters ⭕ EULA-Friendly Global Buffs ✅ Jobs/Skills/Sell Multipliers ✨ Create your own is: 0.046511627906976744
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Transmission is: 0.16666666666666666
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and StatTrackers ⭕ EULA-Friendly Cosmetic ✅ StatTrak in Minecraft ✨ Create Custom Trackers is: 0.06976744186046512
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Firewand is: 0.15625
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Citizen is: 0.16129032258064516
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Rainbow is: 0.16129032258064516
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and SimpleNightVision is: 0.17647058823529413
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoEnchants Extension ⚡ Intimidation is: 0.1388888888888889
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and JoinAndLeave is: 0.08333333333333333
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoBits ⭕ Create custom currencies ✅ Improve your monetization ✨ Supports Shops, MySQL is: 0.08139534883720931
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoShop ⭕ Server Shop GUI ✅ Multi Currency Support ✨ Custom Items, Premade Shop is: 0.06329113924050633
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoQuests ⭕ Powerful Quests System ✅ Fully GUI Based ✨ Quest Trees, Daily Quests, MySQL is: 0.04597701149425287
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and Actions ⭕ Customize your server ✅ Create Scripts, Perks, Automations ✨ 50+ Plugin Hooks is: 0.08045977011494253
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] Similarity between -> Actions and EcoScrolls ⭕ Create Item Modifiers ✅ GUI, Prices, Drag-and-Drop ✨ Custom Item Support is: 0.07058823529411765
[19-07-2025 16:43:01][AP][DEBUG][SpigotSearchByAuthor] [Actions] No match found for Actions!
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Java update was installed successfully (6 -> 9)!
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[19-07-2025 16:43:02][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[19-07-2025 16:43:02][AP][INFO] [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)
[19-07-2025 16:43:02][AP][INFO] 
[19-07-2025 16:43:02][AP][INFO] [OK][SelfUpdater] AutoPlug is on the latest version!
[19-07-2025 16:43:02][AP][INFO] [OK][BackupTask] Completed backup & skipped upload.
[19-07-2025 16:43:02][AP][INFO] [OK][GeneralTasks] Finished.
[19-07-2025 16:43:02][AP][INFO] [OK][DailyRestarter] Restarts at: [3:0]
[19-07-2025 16:43:02][AP][INFO] [OK][CustomRestarter] Skipped.
[19-07-2025 16:43:02][AP][INFO] [OK][JavaUpdater] Java update was installed successfully (6 -> 9)!
[19-07-2025 16:43:02][AP][INFO] [OK][ServerUpdater] Up-to-date version
[19-07-2025 16:43:02][AP][INFO] [OK][PluginsUpdater] Checked 16/16 plugins.
[19-07-2025 16:43:02][AP][INFO] [OK][ModsUpdater] Skipped.
[19-07-2025 16:43:02][AP][INFO] [OK][JavaDownloader] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)
[19-07-2025 16:43:02][AP][INFO] GeneralTasks:
[19-07-2025 16:43:02][AP][INFO] Directory cleaner removed 1 files, from directory ./autoplug/logs
[19-07-2025 16:43:02][AP][INFO] PluginsUpdater:
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] Message: There was an api-error for RoseResourcepack!
[19-07-2025 16:43:02][AP][WARN] Details: Index 0 out of bounds for length 0
[19-07-2025 16:43:02][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[19-07-2025 16:43:02][AP][WARN] Stacktrace: 
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[19-07-2025 16:43:02][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[19-07-2025 16:43:02][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] Message: There was an api-error for OpenJS!
[19-07-2025 16:43:02][AP][WARN] Details: Index 0 out of bounds for length 0
[19-07-2025 16:43:02][AP][WARN] Type: java.lang.IndexOutOfBoundsException
[19-07-2025 16:43:02][AP][WARN] Stacktrace: 
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBounds(Preconditions.java:100)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.outOfBoundsCheckIndex(Preconditions.java:106)
[19-07-2025 16:43:02][AP][WARN] java.base/jdk.internal.util.Preconditions.checkIndex(Preconditions.java:302)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.Objects.checkIndex(Objects.java:385)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.ArrayList.get(ArrayList.java:427)
[19-07-2025 16:43:02][AP][WARN] com.google.gson.JsonArray.get(JsonArray.java:232)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdate(ModrinthAPI.java:73)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.mods.ModrinthAPI.searchUpdatePlugin(ModrinthAPI.java:46)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.ResourceFinder.findPluginByModrinthId(ResourceFinder.java:81)
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.lambda$runAtStart$5(TaskPluginsUpdater.java:328)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
[19-07-2025 16:43:02][AP][WARN] java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
[19-07-2025 16:43:02][AP][WARN] java.base/java.lang.Thread.run(Thread.java:1583)
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] Message: null
[19-07-2025 16:43:02][AP][WARN] Details: Plugin CustomCrops was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[19-07-2025 16:43:02][AP][WARN] Type: java.lang.Exception
[19-07-2025 16:43:02][AP][WARN] Stacktrace: 
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[19-07-2025 16:43:02][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] Message: null
[19-07-2025 16:43:02][AP][WARN] Details: Plugin eco was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[19-07-2025 16:43:02][AP][WARN] Type: java.lang.Exception
[19-07-2025 16:43:02][AP][WARN] Stacktrace: 
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[19-07-2025 16:43:02][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][WARN] Message: null
[19-07-2025 16:43:02][AP][WARN] Details: Plugin Actions was not found by the search-algorithm! Specify an id in the /autoplug/plugins.yml file.
[19-07-2025 16:43:02][AP][WARN] Type: java.lang.Exception
[19-07-2025 16:43:02][AP][WARN] Stacktrace: 
[19-07-2025 16:43:02][AP][WARN] com.osiris.autoplug.client.tasks.updater.plugins.TaskPluginsUpdater.runAtStart(TaskPluginsUpdater.java:377)
[19-07-2025 16:43:02][AP][WARN] com.osiris.betterthread.BThread.run(BThread.java:156)
[19-07-2025 16:43:02][AP][WARN] ================================
[19-07-2025 16:43:02][AP][INFO] Starting server: server.jar
[19-07-2025 16:43:02][AP][DEBUG][Server] Starting server with commands: [/run/media/glitchy/Data/Projects/mc_server/src/autoplug/system/jre/jdk-21.0.7+6/bin/java, -Xms6144M, -Xmx6144M, -jar, server.jar, nogui]
[19-07-2025 16:43:02][AP][DEBUG][Server] process: Process[pid=144131, exitValue="not exited"]
[19-07-2025 16:43:02][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@1018bde2
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Java update was installed successfully (6 -> 9)!
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[19-07-2025 16:43:03][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)
[19-07-2025 16:43:03][AP][INFO] 
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [SelfUpdater][100%] AutoPlug is on the latest version!
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [BackupTask][100%] Completed backup & skipped upload.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [GeneralTasks][100%] Finished.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [DailyRestarter][100%] Restarts at: [3:0]
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [CustomRestarter] Skipped.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [JavaUpdater][100%] Java update was installed successfully (6 -> 9)!
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [ServerUpdater][100%] Up-to-date version
[19-07-2025 16:43:03][AP][INFO] [TASK] [5] > [PluginsUpdater][100%] Checked 16/16 plugins.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [ModsUpdater] Skipped.
[19-07-2025 16:43:03][AP][INFO] [TASK] [#] > [JavaDownloader][100%] Downloaded JDK-21.0.8+9.0.LTS.tar.gz (197mb/197mb)
[19-07-2025 16:43:03][AP][INFO] 
[19-07-2025 20:36:34][AP][INFO] Stopping server...
[19-07-2025 20:36:34][AP][DEBUG][Server] process: Process[pid=144131, exitValue="not exited"]
[19-07-2025 20:36:34][AP][DEBUG][Server] ASYNC_SERVER_IN: com.osiris.autoplug.client.utils.io.AsyncInputStream@1018bde2
[19-07-2025 20:36:34][AP][DEBUG][Server] Stopping server with command: "stop"
[19-07-2025 20:36:37][AP][INFO] See you soon!
[19-07-2025 20:36:37][AP][DEBUG][AL] Stopped AP
[0m