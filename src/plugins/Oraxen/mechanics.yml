# MISCELLANEOUS

commands:
  enabled: true

armor_effects:
  enabled: true
  delay_in_ticks: 20

consumable:
  enabled: true

consumable_potion_effects:
  enabled: true

custom:
  enabled: true

itemtype:
  enabled: true

soulbound:
  enabled: true

backpack:
  enabled: true

music_disc:
  enabled: true

misc:
  enabled: true


# GAMEPLAY

# Specify if custom block sounds should be enabled for the below mechanics
# If both mechanics are disabled, this will force the sound system for those off
custom_block_sounds:
  noteblock_and_block: true
  stringblock_and_furniture: true

# legacy blocks using mushroom stems, prefer to use noteblock
block:
  tool_types:
    - WOODEN
    - STONE
    - IRON
    - GOLDEN
    - DIAMOND
    - NETHERITE
  enabled: false

noteblock:
  tool_types:
    - WOODEN
    - STONE
    - IRON
    - GOLDEN
    - DIAMOND
    - NETHERITE
    - # ticks between each check for dryout
  farmblock_check_delay: 1000
  # Removes the mineable/axe tag from noteblocks for player
  # This is to prevent axes from breaking custom-blocks in normal speed
  remove_mineable_tag: false
  enabled: true

stringblock:
    tool_types:
      - WOODEN
      - STONE
      - IRON
      - GOLDEN
      - DIAMOND
      - NETHERITE
    sapling_growth_check_delay: 4000
    disable_vanilla_strings: false
    enabled: true

furniture:
  default_furniture_type: DISPLAY_ENTITY # DISPLAY_ENTITY, ITEM_FRAME, GLOW_ITEM_FRAME
  tool_types:
    - WOODEN
    - STONE
    - IRON
    - GOLDEN
    - DIAMOND
    - NETHERITE
  # ticks between each check for plant growth
  evolution_check_delay: 200
  enabled: true
  detect_viabackwards: true  # Blocks use of Item Display Furniture when server allows clients below 1.19.4

durability:
  enabled: true

efficiency:
  enabled: true

repair:
  enabled: true
  oraxen_durability_only: false

food:
  enabled: true


# COSMETIC

aura:
  enabled: true

hat:
  enabled: true

skin:
  enabled: true

skinnable:
  enabled: true


# COMBAT

lifeleech:
  enabled: true

energyblast:
  enabled: true

fireball:
  enabled: true

thor:
  enabled: true

witherskull:
  enabled: true


# FARMING

bigmining:
  enabled: true
  # Toggles if Oraxen should call the BlockBreakEvent or just listen to it
  # Known to cause issues with some enchantment-plugins if enabled
  call_events: true

smelting:
  enabled: true
  # To remove an item that should not be cooked, add it here !
  blacklist_cooked:
    - WET_SPONGE

bottledexp:
  enabled: true
  durability_cost: 10

bedrockbreak:
  enabled: true
  disable_on_first_layer: false # Set it on true to avoid player breaking the ground ...completely
  durability_cost: 500

harvesting:
  enabled: true

watering:
  enabled: true
