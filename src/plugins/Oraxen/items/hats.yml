# Material list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html
# EntityType list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/EntityType.html
# Attribute list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/attribute/Attribute.html
# Potion effects list for latest spigot version: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/potion/PotionEffectType.html
# Random UUID generator: https://www.uuidgenerator.net/
# Color codes: https://docs.adventure.kyori.net/minimessage.html#format

anubis_head:
  itemname: <gradient:#3948AE:#6699FF>Anubis Head
  material: PAPER
  lore:
  - <#6f737d>» <#D5D6D8>NightVision when worn
  AttributeModifiers:
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: HEAD
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: HEAD
  Pack:
    generate_model: false
    model: default/anubis_head # .json extension is not mandatory
  # before 1.21.2, simply enable hat mechanic
  Components:
    equippable:
      slot: HEAD
  Mechanics:
    armor_effects:
      night_vision:
        amplifier: 0
        ambient: true # Makes potion effect produce more, translucent, particles.
        particles: true # whether this effect has particles or not
        icon: true # whether this effect has an icon or not

crown:
  itemname: <gradient:#F06966:#FAD6A6>Crown
  material: PAPER
  AttributeModifiers:
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: HEAD
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: HEAD
  Pack:
    generate_model: false
    model: default/crown
  Components:
    equippable:
      slot: HEAD

pharaoh_head:
  itemname: <gradient:#F06966:#FAD6A6>Pharaoh Head
  material: PAPER
  AttributeModifiers:
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: HEAD
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: HEAD
  Pack:
    generate_model: false
    model: default/pharaoh_head
  Components:
    equippable:
      slot: HEAD

witch_hat:
  itemname: <gradient:#DF98FA:#9055FF>Witch Hat
  material: PAPER
  AttributeModifiers:
  - attribute: ARMOR
    amount: 3
    operation: 0
    slot: HEAD
  - attribute: ARMOR_TOUGHNESS
    amount: 2
    operation: 0
    slot: HEAD
  Pack:
    generate_model: false
    model: default/witch_hat
  Components:
    equippable:
      slot: HEAD

space_helmet:
  itemname: <#D5D6D8>Space Helmet
  material: PAPER
  Pack:
    generate_model: false
    model: default/space_helmet
  Components:
    equippable:
      slot: HEAD

